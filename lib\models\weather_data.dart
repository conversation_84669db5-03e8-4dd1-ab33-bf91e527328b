import 'package:flutter/material.dart';

/// Weather data model for OpenWeather API response
class WeatherData {
  final String cityName;
  final String country;
  final double temperature;
  final double feelsLike;
  final double tempMin;
  final double tempMax;
  final int humidity;
  final double windSpeed;
  final int cloudiness;
  final String description;
  final String main;
  final String icon;
  final int pressure;
  final int visibility;
  final DateTime sunrise;
  final DateTime sunset;

  WeatherData({
    required this.cityName,
    required this.country,
    required this.temperature,
    required this.feelsLike,
    required this.tempMin,
    required this.tempMax,
    required this.humidity,
    required this.windSpeed,
    required this.cloudiness,
    required this.description,
    required this.main,
    required this.icon,
    required this.pressure,
    required this.visibility,
    required this.sunrise,
    required this.sunset,
  });

  /// Create WeatherData from OpenWeather API JSON response
  factory WeatherData.fromJson(Map<String, dynamic> json) {
    final main = json['main'];
    final weather = json['weather'][0];
    final wind = json['wind'] ?? {};
    final clouds = json['clouds'] ?? {};
    final sys = json['sys'] ?? {};

    return WeatherData(
      cityName: json['name'] ?? 'Unknown',
      country: sys['country'] ?? '',
      temperature: (main['temp'] as num).toDouble(),
      feelsLike: (main['feels_like'] as num).toDouble(),
      tempMin: (main['temp_min'] as num).toDouble(),
      tempMax: (main['temp_max'] as num).toDouble(),
      humidity: main['humidity'] ?? 0,
      windSpeed: (wind['speed'] as num?)?.toDouble() ?? 0.0,
      cloudiness: clouds['all'] ?? 0,
      description: weather['description'] ?? '',
      main: weather['main'] ?? '',
      icon: weather['icon'] ?? '01d',
      pressure: main['pressure'] ?? 0,
      visibility: json['visibility'] ?? 0,
      sunrise: DateTime.fromMillisecondsSinceEpoch(
        (sys['sunrise'] ?? 0) * 1000,
      ),
      sunset: DateTime.fromMillisecondsSinceEpoch((sys['sunset'] ?? 0) * 1000),
    );
  }

  /// Get temperature in Celsius (rounded)
  int get temperatureCelsius => temperature.round();

  /// Get feels like temperature in Celsius (rounded)
  int get feelsLikeCelsius => feelsLike.round();

  /// Get min temperature in Celsius (rounded)
  int get tempMinCelsius => tempMin.round();

  /// Get max temperature in Celsius (rounded)
  int get tempMaxCelsius => tempMax.round();

  /// Get wind speed in km/h
  double get windSpeedKmh => windSpeed * 3.6;

  /// Get formatted wind speed
  String get formattedWindSpeed => '${windSpeedKmh.round()} km/h';

  /// Get formatted humidity
  String get formattedHumidity => '$humidity%';

  /// Get formatted cloudiness
  String get formattedCloudiness => '$cloudiness%';

  /// Get formatted description (capitalized)
  String get formattedDescription {
    return description
        .split(' ')
        .map((word) {
          return word.isNotEmpty
              ? word[0].toUpperCase() + word.substring(1)
              : word;
        })
        .join(' ');
  }

  /// Get weather icon based on main weather condition
  String get weatherIcon {
    switch (main.toLowerCase()) {
      case 'clear':
        return '☀️';
      case 'clouds':
        return cloudiness > 50 ? '☁️' : '⛅';
      case 'rain':
        return '🌧️';
      case 'drizzle':
        return '🌦️';
      case 'thunderstorm':
        return '⛈️';
      case 'snow':
        return '❄️';
      case 'mist':
      case 'fog':
      case 'haze':
        return '🌫️';
      default:
        return '🌤️';
    }
  }

  /// Get background gradient colors based on weather
  List<Color> get backgroundColors {
    switch (main.toLowerCase()) {
      case 'clear':
        return [
          const Color(0xFF87CEEB), // Sky blue
          const Color(0xFF4682B4), // Steel blue
        ];
      case 'clouds':
        return [
          const Color(0xFF708090), // Slate gray
          const Color(0xFF2F4F4F), // Dark slate gray
        ];
      case 'rain':
      case 'drizzle':
        return [
          const Color(0xFF4682B4), // Steel blue
          const Color(0xFF191970), // Midnight blue
        ];
      case 'thunderstorm':
        return [
          const Color(0xFF2F4F4F), // Dark slate gray
          const Color(0xFF000000), // Black
        ];
      case 'snow':
        return [
          const Color(0xFFB0C4DE), // Light steel blue
          const Color(0xFF4682B4), // Steel blue
        ];
      default:
        return [
          const Color(0xFF87CEEB), // Sky blue
          const Color(0xFF4682B4), // Steel blue
        ];
    }
  }
}

/// Location data for weather requests
class LocationData {
  final double latitude;
  final double longitude;
  final String? cityName;

  LocationData({
    required this.latitude,
    required this.longitude,
    this.cityName,
  });
}
