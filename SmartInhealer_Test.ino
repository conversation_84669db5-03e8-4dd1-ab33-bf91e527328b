#include "BLEDevice.h"
#include "BLEServer.h"
#include "BLEUtils.h"
#include "BLE2902.h"

// BLE Service and Characteristic UUIDs (must match Flutter app)
#define SERVICE_UUID        "4fafc201-1fb5-459e-8fcc-c5c9c331914b"
#define DATA_CHAR_UUID      "beb5483e-36e1-4688-b7f5-ea07361b26a8"
#define STATUS_CHAR_UUID    "beb5483e-36e1-4688-b7f5-ea07361b26a9"

// Device name
#define DEVICE_NAME "SmartInhealer"

// BLE objects
BLEServer* pServer = NULL;
BLECharacteristic* pDataCharacteristic = NULL;
BLECharacteristic* pStatusCharacteristic = NULL;
bool deviceConnected = false;

// Function declarations
void sendInitialStatus();

class MyServerCallbacks: public BLEServerCallbacks {
    void onConnect(BLEServer* pServer) {
      deviceConnected = true;
      Serial.println("Device connected!");
      // Send initial status when connected
      delay(1000); // Give time for characteristics to be ready
      sendInitialStatus();
    };

    void onDisconnect(BLEServer* pServer) {
      deviceConnected = false;
      Serial.println("Device disconnected!");
      // Restart advertising
      delay(500);
      pServer->startAdvertising();
      Serial.println("Restarting advertising...");
    }
};

class MyDataCallbacks: public BLECharacteristicCallbacks {
    void onWrite(BLECharacteristic *pCharacteristic) {
      String rxValue = pCharacteristic->getValue().c_str();

      if (rxValue.length() > 0) {
        Serial.print("Received: ");
        Serial.println(rxValue);
        
        // Echo back the command for testing
        if (pDataCharacteristic) {
          String response = "Echo: ";
          response += rxValue;
          pDataCharacteristic->setValue(response.c_str());
          pDataCharacteristic->notify();
          Serial.print("Sent back: ");
          Serial.println(response);
        }
      }
    }
};

void setup() {
  Serial.begin(115200);
  Serial.println("Starting SmartInhealer BLE Test Server...");

  // Initialize BLE
  BLEDevice::init(DEVICE_NAME);
  pServer = BLEDevice::createServer();
  pServer->setCallbacks(new MyServerCallbacks());

  // Create BLE Service
  BLEService *pService = pServer->createService(SERVICE_UUID);

  // Create Data Characteristic (Read/Write/Notify)
  pDataCharacteristic = pService->createCharacteristic(
                         DATA_CHAR_UUID,
                         BLECharacteristic::PROPERTY_READ |
                         BLECharacteristic::PROPERTY_WRITE |
                         BLECharacteristic::PROPERTY_NOTIFY
                       );
  pDataCharacteristic->setCallbacks(new MyDataCallbacks());
  pDataCharacteristic->addDescriptor(new BLE2902());

  // Create Status Characteristic (Read/Notify)
  pStatusCharacteristic = pService->createCharacteristic(
                           STATUS_CHAR_UUID,
                           BLECharacteristic::PROPERTY_READ |
                           BLECharacteristic::PROPERTY_NOTIFY
                         );
  pStatusCharacteristic->addDescriptor(new BLE2902());

  // Start the service
  pService->start();

  // Start advertising
  BLEAdvertising *pAdvertising = BLEDevice::getAdvertising();
  pAdvertising->addServiceUUID(SERVICE_UUID);
  pAdvertising->setScanResponse(false);
  pAdvertising->setMinPreferred(0x0);
  BLEDevice::startAdvertising();
  
  Serial.println("SmartInhealer BLE Test Server started!");
  Serial.println("Device name: " + String(DEVICE_NAME));
  Serial.println("Service UUID: " + String(SERVICE_UUID));
  Serial.println("Waiting for client connection...");
}

void loop() {
  // Send periodic status updates when connected
  static unsigned long lastUpdate = 0;
  if (deviceConnected && millis() - lastUpdate > 5000) { // Every 5 seconds
    sendStatusUpdate();
    lastUpdate = millis();
  }
  
  delay(1000);
}

void sendInitialStatus() {
  if (!deviceConnected || !pStatusCharacteristic) return;
  
  // Simple JSON status
  String status = "{\"status\":\"ready\",\"battery\":85,\"device\":\"SmartInhealer\"}";
  
  pStatusCharacteristic->setValue(status.c_str());
  pStatusCharacteristic->notify();
  
  Serial.print("Initial status sent: ");
  Serial.println(status);
}

void sendStatusUpdate() {
  if (!deviceConnected || !pStatusCharacteristic) return;
  
  // Get battery level (simulated)
  int batteryLevel = random(70, 100); // Random battery level for testing
  
  // Create simple JSON status
  String status = "{";
  status += "\"status\":\"ready\",";
  status += "\"battery\":";
  status += String(batteryLevel);
  status += ",\"timestamp\":";
  status += String(millis());
  status += "}";
  
  pStatusCharacteristic->setValue(status.c_str());
  pStatusCharacteristic->notify();
  
  Serial.print("Status update: ");
  Serial.println(status);
}
