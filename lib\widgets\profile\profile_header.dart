import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
//import '../../services/file_storage_service.dart';
import 'package:befine/pages/patient/edit_profile_screen.dart';
import 'package:befine/theme/app_theme.dart'; // Added app theme import

class ProfileHeader extends StatelessWidget {
  final String userRole;

  const ProfileHeader({Key? key, required this.userRole}) : super(key: key);

  Future<Map<String, String>> _loadUserData() async {
    // Get login state to retrieve user ID
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? userEmail = prefs.getString('userEmail');
    String? userRole = prefs.getString('userRole');

    if (userEmail == null || userRole == null) {
      return _getDefaultUserData();
    }

    // Initialize file storage service
    final fileStorageService = null; //await FileStorageService.getInstance();

    // Get login state to retrieve user ID
    final loginState = await fileStorageService.getLoginState();
    if (loginState == null) {
      return _getDefaultUserData();
    }

    final userId = loginState['user_id'];

    try {
      // Get user profile data
      final userProfile = await fileStorageService.getUserProfile(userId);

      if (userProfile == null) {
        return _getDefaultUserData();
      }

      // Get role-specific data
      if (userRole == 'patient') {
        final patientData = await fileStorageService.getPatientData(userId);

        if (patientData == null) {
          return _getDefaultUserData();
        }

        // Create a new map with the required fields
        Map<String, String> result = {
          'name': '${userProfile.firstName} ${userProfile.lastName}',
          'email': userProfile.email,
          'phone': patientData.tel,
          'role': 'patient',
          'profileImage': 'assets/icons/patient_icon.png',
          'medicalHistory': patientData.medicalHistory,
          'height': '${patientData.height.toString()} cm',
          'weight': '${patientData.weight.toString()} kg',
          'age': patientData.age.toString(),
          'activityLevel': patientData.activityLevel,
          'emergencyContact': patientData.emergencyContact,
          'birthday': _formatDate(patientData.birthday.toString()),
          'gender': patientData.gender,
        };

        return result;
        
      } else if (userRole == 'doctor') {
        final doctorData = await fileStorageService.getDoctorData(userId);

        if (doctorData == null) {
          return _getDefaultUserData();
        }

        // Create a new map with the required fields for doctor
        Map<String, String> result = {
          'name': '${userProfile.firstName} ${userProfile.lastName}',
          'email': userProfile.email,
          'phone': 'Not provided', // Doctors might not have phone in the model
          'role': 'doctor',
          'profileImage': 'assets/icons/doctor_icon.png',
          'specialization': doctorData.specialization,
        };

        return result;
      }
    } catch (e) {
      print('Error loading user data: $e');
    }

    return _getDefaultUserData();
  }

  // Helper method to return default user data
  Map<String, String> _getDefaultUserData() {
    return {
      'name': 'Guest',
      'email': 'Not provided',
      'phone': 'Not provided',
      'role': userRole,
      'profileImage': 'assets/icons/default_avatar.png',
      'medicalHistory': 'Not provided',
      'height': 'Not provided',
      'weight': 'Not provided',
      'emergencyContact': 'Not provided',
      'birthday': 'Not provided',
      'gender': 'Not provided',
    };
  }

  // Helper method to format date strings
  String _formatDate(String? dateString) {
    if (dateString == null) return 'Not provided';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'Not provided';
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, dynamic>>(
      future: _loadUserData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: CircularProgressIndicator(
              color: AppTheme.primaryColor,
            ),
          );
        }

        if (!snapshot.hasData) {
          return Center(
            child: Text(
              'No user data available',
              style: TextStyle(color: AppTheme.textSecondaryColor),
            ),
          );
        }

        final userData = snapshot.data!;

        return Card(
          margin: const EdgeInsets.all(16.0),
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          color: AppTheme.surfaceColor,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppTheme.surfaceColor,
                        border: Border.all(
                          color: AppTheme.primaryColor,
                          width: 2,
                        ),
                      ),
                      child: CircleAvatar(
                        radius: 40,
                        backgroundColor: AppTheme.surfaceColor,
                        backgroundImage: AssetImage(userData['profileImage']),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            userData['name'],
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppTheme.textPrimaryColor,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            userData['email'],
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                          Text(
                            'Phone: ${userData['phone']}',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.edit,
                        color: AppTheme.primaryColor,
                      ),
                      tooltip: 'Edit Profile',
                      onPressed: () async {
                        final result = await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const EditProfileScreen(),
                          ),
                        );

                        if (result == true && context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: const Text('Profile updated'),
                              backgroundColor: AppTheme.primaryColor,
                            ),
                          );
                        }
                      },
                    ),
                  ],
                ),
                if (userRole == 'patient') ...[
                  Divider(
                    height: 32,
                    color: AppTheme.textTertiaryColor,
                    thickness: 1,
                  ),
                  Text(
                    'Medical Information',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildInfoRow('Height', '${userData['height']} cm'),
                  _buildInfoRow('Weight', '${userData['weight']} kg'),
                  _buildInfoRow('Birthday', userData['birthday']),
                  _buildInfoRow('Gender', userData['gender']),
                  _buildInfoRow('Medical History', userData['medicalHistory']),
                  _buildInfoRow(
                    'Emergency Contact',
                    userData['emergencyContact'],
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 140,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: AppTheme.textSecondaryColor),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateString(String? dateStr) {
    if (dateStr == null) return 'Not provided';
    try {
      final date = DateTime.parse(dateStr);
      return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'Invalid date';
    }
  }
}
