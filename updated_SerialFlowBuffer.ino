#include <Arduino.h>
#include <SensirionI2CSdp.h>
#include <Wire.h>
#include <BLEDevice.h>
#include <BLEServer.h>
#include <BLEUtils.h>
#include <BLE2902.h>
#include <ArduinoJson.h>

// Function declarations (to avoid scope issues)
void startMeasurement();
void stopMeasurement();
void takeMeasurement();
void resetMeasurement();
int getBatteryLevel();

// I2C Configuration for ESP32-C6
#define I2C_SDA_PIN 6       // I2C SDA pin number
#define I2C_SCL_PIN 7       // I2C SCL pin number

// Battery monitoring configuration
#define BATTERY_PIN 5       // ADC pin for battery voltage monitoring
#define BATTERY_READ_INTERVAL 10000 // Read battery every 10 seconds

// BLE Configuration - Match with Device Model UUIDs
#define SERVICE_UUID        "4fafc201-1fb5-459e-8fcc-c5c9c331914b"
#define DATA_CHARACTERISTIC_UUID "beb5483e-36e1-4688-b7f5-ea07361b26a8"
#define STATUS_CHARACTERISTIC_UUID "beb5483e-36e1-4688-b7f5-ea07361b26a9"

// Device information
#define DEVICE_NAME "SmartInhealer"
#define DEVICE_TYPE "ESP32C6"

BLEServer* pServer = NULL;
BLECharacteristic* pDataCharacteristic = NULL;
BLECharacteristic* pStatusCharacteristic = NULL;
bool deviceConnected = false;
bool oldDeviceConnected = false;

// Sensor Configuration
SensirionI2CSdp sdp;        // Differential pressure sensor object

// Flow calculation parameters
const float K_FACTOR = 0.1; // Sensor calibration factor
const float DENSITY = 1.204; // Air density at 20°C [kg/m³]

// Test configuration
const int MAX_SAMPLES = 128;  // Maximum number of samples to collect
int sampleCount = 0;          // Current sample count

// Measurement control
bool isMeasuring = false;
unsigned long startTime = 0;
float totalVolume = 0.0;
unsigned long lastMeasurementTime = 0;
unsigned long lastBatteryReadTime = 0;
const int MEASUREMENT_INTERVAL_MS = 50; // 20Hz sampling rate

// Battery level (0-100%)
int batteryLevel = 0;
int storageStatus = 0; // 0=unknown, 1=ok, 2=warning, 3=full

// BLE callbacks
class MyServerCallbacks: public BLEServerCallbacks {
  void onConnect(BLEServer* pServer) {
    deviceConnected = true;
    Serial.println("Device connected");
  }

  void onDisconnect(BLEServer* pServer) {
    deviceConnected = false;
    Serial.println("Device disconnected");
    
    // Stop measurement if active when disconnected
    if (isMeasuring) {
      stopMeasurement();
    }
  }
};

// Modified callback to avoid std::string issues
class MyCharacteristicCallbacks: public BLECharacteristicCallbacks {
  void onWrite(BLECharacteristic *pCharacteristic) {
    String command = "";

    // Process the received message byte by byte to avoid string conversion issues
    uint8_t* dataPtr = pCharacteristic->getData();
    size_t dataLen = pCharacteristic->getLength();

    // Convert bytes to a String object
    for (int i = 0; i < dataLen; i++) {
      command += (char)dataPtr[i];
    }

    if (command.length() > 0) {
      Serial.print("Received command: ");
      Serial.println(command);

      command.trim(); // Remove any whitespace

      if (command.equals("START_MEASUREMENT")) {
        startMeasurement();
      } else if (command.equals("STOP_MEASUREMENT")) {
        stopMeasurement();
      } else if (command.equals("RESET_MEASUREMENT")) {
        resetMeasurement();
      }
    }
  }
};

void setup() {
  Serial.begin(115200);     // Initialize serial communication
  
  // Initialize ADC for battery monitoring
  analogReadResolution(12); // Set ADC resolution to 12 bits (0-4095)
  pinMode(BATTERY_PIN, INPUT);
  
  // Read initial battery level
  batteryLevel = getBatteryLevel();
  
  // Initialize BLE
  BLEDevice::init(DEVICE_NAME);

  // Create the BLE Server
  pServer = BLEDevice::createServer();
  pServer->setCallbacks(new MyServerCallbacks());

  // Create the BLE Service
  BLEService *pService = pServer->createService(SERVICE_UUID);

  // Create a BLE Characteristic for data transmission
  pDataCharacteristic = pService->createCharacteristic(
                    DATA_CHARACTERISTIC_UUID,
                    BLECharacteristic::PROPERTY_READ |
                    BLECharacteristic::PROPERTY_WRITE |
                    BLECharacteristic::PROPERTY_NOTIFY
                  );

  pDataCharacteristic->setCallbacks(new MyCharacteristicCallbacks());
  pDataCharacteristic->addDescriptor(new BLE2902());

  // Create a BLE Characteristic for status updates
  pStatusCharacteristic = pService->createCharacteristic(
                    STATUS_CHARACTERISTIC_UUID,
                    BLECharacteristic::PROPERTY_READ |
                    BLECharacteristic::PROPERTY_NOTIFY
                  );
  pStatusCharacteristic->addDescriptor(new BLE2902());

  // Start the service
  pService->start();

  // Start advertising
  BLEAdvertisementData advData;
  advData.setName(DEVICE_NAME);
  advData.setCompleteServices(BLEUUID(SERVICE_UUID));
  
  pServer->getAdvertising()->setAdvertisementData(advData);
  pServer->getAdvertising()->start();
  Serial.println("BLE server started, waiting for connections...");

  // Initialize I2C and configure pressure sensor
  Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN);

  // Configure pressure sensor
  sdp.begin(Wire, SDP3X_I2C_ADDRESS_0);
  sdp.stopContinuousMeasurement();
  delay(50);
  sdp.startContinuousMeasurementWithDiffPressureTCompAndAveraging();

  Serial.println("SmartInhealer Flow Sensor started. Connect using BLE.");
}

void loop() {
  // Check if it's time to read battery level
  if (millis() - lastBatteryReadTime >= BATTERY_READ_INTERVAL) {
    lastBatteryReadTime = millis();
    batteryLevel = getBatteryLevel();
    
    // Send battery update if connected
    if (deviceConnected) {
      sendDeviceStatus();
    }
  }
  
  // If we're measuring and it's time for a new sample
  if (isMeasuring && (millis() - lastMeasurementTime >= MEASUREMENT_INTERVAL_MS)) {
    lastMeasurementTime = millis();
    takeMeasurement();

    // Check if we've reached the maximum number of samples
    if (sampleCount >= MAX_SAMPLES) {
      stopMeasurement();

      // Send test complete status
      if (deviceConnected) {
        String statusMsg = "{\"status\":\"complete\",\"samples\":" + String(sampleCount) + 
                          ",\"batteryLevel\":" + String(batteryLevel) + 
                          ",\"storageStatus\":" + String(storageStatus) + "}";
        pStatusCharacteristic->setValue(statusMsg.c_str());
        pStatusCharacteristic->notify();
        Serial.println("Test completed automatically after " + String(sampleCount) + " samples");
      }
    }
  }

  // Handle BLE connection status changes
  if (!deviceConnected && oldDeviceConnected) {
    delay(500); // Give the bluetooth stack time to get things ready
    pServer->startAdvertising(); // Restart advertising
    Serial.println("Restarting advertising");
    oldDeviceConnected = deviceConnected;
  }

  // Connection established
  if (deviceConnected && !oldDeviceConnected) {
    oldDeviceConnected = deviceConnected;
    
    // Send initial device status
    sendDeviceStatus();
  }
}

// Read battery level from ADC
int getBatteryLevel() {
  // Read the analog value from the battery pin
  int rawValue = analogRead(BATTERY_PIN);
  
  // Convert ADC reading to voltage (assuming 3.3V reference)
  // For a voltage divider with R1=100k and R2=100k, the formula would be:
  // Vbat = ADC * (3.3 / 4095) * 2
  float voltage = rawValue * (3.3 / 4095.0) * 2.0;
  
  // Convert voltage to battery percentage (assuming LiPo 3.7V battery)
  // 3.0V = 0%, 4.2V = 100%
  int percentage = map(voltage * 100, 300, 420, 0, 100);
  
  // Constrain to valid range
  percentage = constrain(percentage, 0, 100);
  
  Serial.print("Battery voltage: ");
  Serial.print(voltage);
  Serial.print("V, Level: ");
  Serial.print(percentage);
  Serial.println("%");
  
  return percentage;
}

// Send device status including battery level
void sendDeviceStatus() {
  if (!deviceConnected) return;
  
  String statusMsg = "{\"status\":\"" + String(isMeasuring ? "measuring" : "ready") + 
                    "\",\"maxSamples\":" + String(MAX_SAMPLES) + 
                    ",\"batteryLevel\":" + String(batteryLevel) + 
                    ",\"storageStatus\":" + String(storageStatus) + 
                    ",\"deviceType\":\"" + String(DEVICE_TYPE) + "\"}";
  
  pStatusCharacteristic->setValue(statusMsg.c_str());
  pStatusCharacteristic->notify();
  Serial.println("Sent status: " + statusMsg);
}

void startMeasurement() {
  Serial.println("Starting measurement");
  isMeasuring = true;
  startTime = millis();
  totalVolume = 0.0;
  sampleCount = 0;

  // Send status update
  if (deviceConnected) {
    String statusMsg = "{\"status\":\"measuring\",\"samples\":0,\"batteryLevel\":" + 
                      String(batteryLevel) + ",\"storageStatus\":" + String(storageStatus) + "}";
    pStatusCharacteristic->setValue(statusMsg.c_str());
    pStatusCharacteristic->notify();
  }
}

void stopMeasurement() {
  Serial.println("Stopping measurement");
  isMeasuring = false;

  // Send status update
  if (deviceConnected) {
    String statusMsg = "{\"status\":\"stopped\",\"samples\":" + String(sampleCount) + 
                      ",\"batteryLevel\":" + String(batteryLevel) + 
                      ",\"storageStatus\":" + String(storageStatus) + "}";
    pStatusCharacteristic->setValue(statusMsg.c_str());
    pStatusCharacteristic->notify();
  }
}

void resetMeasurement() {
  Serial.println("Resetting measurement");
  isMeasuring = false;
  sampleCount = 0;
  totalVolume = 0.0;

  // Send status update
  if (deviceConnected) {
    String statusMsg = "{\"status\":\"ready\",\"maxSamples\":" + String(MAX_SAMPLES) + 
                      ",\"batteryLevel\":" + String(batteryLevel) + 
                      ",\"storageStatus\":" + String(storageStatus) + "}";
    pStatusCharacteristic->setValue(statusMsg.c_str());
    pStatusCharacteristic->notify();
  }
}

void takeMeasurement() {
  float differentialPressure, temperature;
  if(!sdp.readMeasurement(differentialPressure, temperature)) {
    // Calculate instantaneous flow rate in liters/second
    float flowRate = sqrt(fabs(differentialPressure) / DENSITY) * K_FACTOR ;
    if(differentialPressure > 0) flowRate *= -1; // Adjust for sensor orientation

    // Calculate elapsed time in seconds
    float elapsedTime = (millis() - startTime) / 1000.0;

    // Calculate volume increment (flow * time interval)
    float volumeIncrement = -1 * flowRate * (MEASUREMENT_INTERVAL_MS / 1000.0);
    totalVolume += volumeIncrement;

    // Increment sample count
    sampleCount++;

    // Create JSON document
    StaticJsonDocument<192> doc;
    doc["time"] = elapsedTime;
    doc["flow"] = flowRate;
    doc["volume"] = totalVolume;
    doc["sample"] = sampleCount;
    doc["progress"] = (float)sampleCount / MAX_SAMPLES * 100.0;
    doc["batteryLevel"] = batteryLevel;

    // Serialize JSON to a String
    String jsonString;
    serializeJson(doc, jsonString);

    // Send JSON via BLE if connected
    if (deviceConnected) {
      pDataCharacteristic->setValue(jsonString.c_str());
      pDataCharacteristic->notify();

      // Update status every 10 samples
      if (sampleCount % 10 == 0) {
        String statusMsg = "{\"status\":\"measuring\",\"samples\":" + String(sampleCount) + 
                          ",\"progress\":" + String((float)sampleCount / MAX_SAMPLES * 100.0) + 
                          ",\"batteryLevel\":" + String(batteryLevel) + "}";
        pStatusCharacteristic->setValue(statusMsg.c_str());
        pStatusCharacteristic->notify();
      }
    }

    // Also send to Serial for debugging
    Serial.println(jsonString);
  }
}
