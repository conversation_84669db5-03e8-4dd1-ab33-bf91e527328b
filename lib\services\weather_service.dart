import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';
import 'package:befine/models/weather_data.dart';

/// Service for fetching weather data from OpenWeather API
class WeatherService extends ChangeNotifier {
  static const String _apiKey = '4da32750991c422d43477f542f799978';
  static const String _baseUrl = 'https://api.openweathermap.org/data/2.5';
  
  WeatherData? _currentWeather;
  bool _isLoading = false;
  String? _error;
  LocationData? _currentLocation;

  // Getters
  WeatherData? get currentWeather => _currentWeather;
  bool get isLoading => _isLoading;
  String? get error => _error;
  LocationData? get currentLocation => _currentLocation;

  /// Get current weather data for user's location
  Future<void> getCurrentWeather() async {
    try {
      _setLoading(true);
      _clearError();

      // Get user's current location
      final location = await _getCurrentLocation();
      if (location == null) {
        throw Exception('Unable to get current location');
      }

      _currentLocation = location;

      // Fetch weather data
      final weather = await _fetchWeatherByCoordinates(
        location.latitude,
        location.longitude,
      );

      _currentWeather = weather;
      debugPrint('Weather data fetched successfully for ${weather.cityName}');
    } catch (e) {
      _setError('Failed to fetch weather data: ${e.toString()}');
      debugPrint('Error fetching weather: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Get weather data by city name
  Future<void> getWeatherByCity(String cityName) async {
    try {
      _setLoading(true);
      _clearError();

      final weather = await _fetchWeatherByCity(cityName);
      _currentWeather = weather;
      debugPrint('Weather data fetched successfully for $cityName');
    } catch (e) {
      _setError('Failed to fetch weather data: ${e.toString()}');
      debugPrint('Error fetching weather for $cityName: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh current weather data
  Future<void> refreshWeather() async {
    if (_currentLocation != null) {
      await getCurrentWeather();
    } else {
      await getCurrentWeather();
    }
  }

  /// Get current location
  Future<LocationData?> _getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Location services are disabled');
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      return LocationData(
        latitude: position.latitude,
        longitude: position.longitude,
      );
    } catch (e) {
      debugPrint('Error getting location: $e');
      return null;
    }
  }

  /// Fetch weather data by coordinates
  Future<WeatherData> _fetchWeatherByCoordinates(
    double latitude,
    double longitude,
  ) async {
    final url = Uri.parse(
      '$_baseUrl/weather?lat=$latitude&lon=$longitude&appid=$_apiKey&units=metric',
    );

    final response = await http.get(url);

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return WeatherData.fromJson(data);
    } else {
      throw Exception('Failed to load weather data: ${response.statusCode}');
    }
  }

  /// Fetch weather data by city name
  Future<WeatherData> _fetchWeatherByCity(String cityName) async {
    final url = Uri.parse(
      '$_baseUrl/weather?q=$cityName&appid=$_apiKey&units=metric',
    );

    final response = await http.get(url);

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return WeatherData.fromJson(data);
    } else {
      throw Exception('Failed to load weather data: ${response.statusCode}');
    }
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// Clear all data
  void clearData() {
    _currentWeather = null;
    _currentLocation = null;
    _error = null;
    _isLoading = false;
    notifyListeners();
  }
}
