import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:befine/models/measurement_data.dart';
import 'package:befine/theme/app_theme.dart';

/// Widget for displaying flow vs volume chart
class FlowVolume<PERSON>hart extends StatelessWidget {
  /// Flow data points
  final List<MeasurementPoint> flowData;
  
  /// Volume data points
  final List<MeasurementPoint> volumeData;
  
  /// Optional maximum Y value (flow)
  final double? maxY;
  
  /// Optional minimum Y value (flow)
  final double? minY;
  
  /// Optional maximum X value (volume)
  final double? maxX;
  
  /// Optional minimum X value (volume)
  final double? minX;
  
  /// Whether to use dynamic scaling based on data
  final bool useDynamicScaling;

  /// Constructor
  const FlowVolumeChart({
    super.key,
    required this.flowData,
    required this.volumeData,
    this.maxY, // Optional max flow rate (L/s)
    this.minY, // Optional min flow rate (L/s)
    this.maxX, // Optional max volume (L)
    this.minX, // Optional min volume (L)
    this.useDynamicScaling = true, // Use dynamic scaling by default
  });

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1.5,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Courbe Débit/Volume (Inspiration)',
              style: TextStyle(
                fontSize: 18, 
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(child: LineChart(_flowVolumeChartData())),
          ],
        ),
      ),
    );
  }

  LineChartData _flowVolumeChartData() {
    // If we don't have enough data, return empty chart
    if (flowData.isEmpty || volumeData.isEmpty) {
      return LineChartData(
        gridData: FlGridData(show: true),
        titlesData: FlTitlesData(show: true),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: Colors.grey.shade300),
        ),
      );
    }

    // Create paired data points (volume, flow)
    // We need to match flow and volume data points that have the same time
    List<FlSpot> spots = [];

    // Assuming flowData and volumeData have the same length and time points
    // We'll use the volume as X and flow as Y
    // For inhalation, flow values are negative, and we'll keep them negative
    for (int i = 0; i < flowData.length && i < volumeData.length; i++) {
      // Only add points if we have valid volume (x-axis) data and if it's inhalation (negative flow)
      if ((volumeData[i].value != 0 || spots.isEmpty) &&
          flowData[i].value < 0) {
        // Keep the flow value negative for proper display with zero at top
        spots.add(FlSpot(volumeData[i].value, flowData[i].value));
      }
    }

    // Calculate dynamic min and max Y values (flow) if needed
    // For inhalation, we're dealing with negative flow values
    double effectiveMinY;
    double effectiveMaxY;

    if (useDynamicScaling && flowData.isNotEmpty) {
      // Filter for inhalation values only (negative flow values)
      List<double> inhalationValues =
          flowData
              .where((p) => p.value < 0)
              .map((p) => p.value) // Keep negative values
              .toList();

      if (inhalationValues.isEmpty) {
        // If no inhalation values, use defaults
        effectiveMaxY = 0.0; // Zero at top
        effectiveMinY = minY ?? -5.0; // Negative values below
      } else {
        // Find min and max values in the inhalation flow data
        double dataMinY = inhalationValues.reduce(
          (a, b) => a < b ? a : b,
        ); // Most negative
        double dataMaxY = inhalationValues.reduce(
          (a, b) => a > b ? a : b,
        ); // Least negative

        // Add some padding (10% of the range)
        double range = (dataMaxY - dataMinY).abs();
        double padding = range * 0.1;

        // Ensure we have at least some range
        if (range < 0.1) {
          padding = 0.5; // Default padding if range is very small
        }

        // For inhalation, max Y is always 0 (zero at top)
        effectiveMaxY = 0.0;
        effectiveMinY = dataMinY - padding; // More negative with padding
      }
    } else {
      // Use provided values or defaults
      // For inhalation, max Y is always 0 (zero at top)
      effectiveMaxY = 0.0;
      effectiveMinY = minY ?? -5.0;
    }

    // Calculate dynamic min and max X values (volume) if needed
    double effectiveMinX;
    double effectiveMaxX;

    if (useDynamicScaling && volumeData.isNotEmpty) {
      // Find min and max values in the volume data
      double dataMinX = volumeData
          .map((p) => p.value)
          .reduce((a, b) => a < b ? a : b);
      double dataMaxX = volumeData
          .map((p) => p.value)
          .reduce((a, b) => a > b ? a : b);

      // Add some padding (10% of the range)
      double range = (dataMaxX - dataMinX).abs();
      double padding = range * 0.1;

      // Ensure we have at least some range
      if (range < 0.1) {
        padding = 0.5; // Default padding if range is very small
      }

      effectiveMinX = dataMinX - padding;
      effectiveMaxX = dataMaxX + padding;

      // For volume, we typically want to start at 0 unless we have negative values
      if (effectiveMinX > 0) effectiveMinX = 0;
    } else {
      // Use provided values or defaults
      effectiveMinX = minX ?? 0.0;
      effectiveMaxX = maxX ?? 6.0;
    }

    // Calculate appropriate interval for Y axis (flow)
    double yRange = (effectiveMaxY - effectiveMinY).abs();
    double yInterval = 1.0; // Default interval

    if (yRange > 20) {
      yInterval = 5.0;
    } else if (yRange > 10) {
      yInterval = 2.0;
    } else if (yRange < 5) {
      yInterval = 0.5;
    }

    // Calculate appropriate interval for X axis (volume)
    double xRange = (effectiveMaxX - effectiveMinX).abs();
    double xInterval = 1.0; // Default interval

    if (xRange > 20) {
      xInterval = 5.0;
    } else if (xRange > 10) {
      xInterval = 2.0;
    } else if (xRange < 2) {
      xInterval = 0.2;
    } else if (xRange < 5) {
      xInterval = 0.5;
    }

    return LineChartData(
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        horizontalInterval: yInterval,
        verticalInterval: xInterval,
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: xInterval,
            getTitlesWidget: (value, meta) {
              // Format with 1 decimal place if interval is less than 1
              String label =
                  xInterval < 1
                      ? value.toStringAsFixed(1)
                      : value.toInt().toString();
              return Text(
                label,
                style: TextStyle(
                  color: AppTheme.textPrimaryColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              );
            },
          ),
          axisNameWidget: Text(
            'Volume (L)',
            style: TextStyle(
              fontSize: 14, 
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: yInterval,
            getTitlesWidget: (value, meta) {
              // Format with 1 decimal place if interval is less than 1
              String label =
                  yInterval < 1
                      ? value.toStringAsFixed(1)
                      : value.toInt().toString();
              return Text(
                label,
                style: TextStyle(
                  color: AppTheme.textPrimaryColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              );
            },
            reservedSize: 40,
          ),
          axisNameWidget: Text(
            'Débit (L/s)',
            style: TextStyle(
              fontSize: 14, 
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: Colors.grey.shade300),
      ),
      minX: effectiveMinX,
      maxX: effectiveMaxX,
      minY: effectiveMinY,
      maxY: effectiveMaxY,
      lineBarsData: [
        LineChartBarData(
          spots: spots,
          isCurved: true,
          color: AppTheme.primaryColor,
          barWidth: 3,
          isStrokeCapRound: true,
          dotData: const FlDotData(show: false),
          belowBarData: BarAreaData(
            show: true,
            // Fill from curve to x-axis (y=0)
            color: AppTheme.primaryColor.withAlpha(80),
            cutOffY: 0, // Fill up to y=0 (x-axis)
            applyCutOffY: true,
          ),
        ),
      ],
    );
  }
}
