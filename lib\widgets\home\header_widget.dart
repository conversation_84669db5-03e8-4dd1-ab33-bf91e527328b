import 'package:flutter/material.dart';
//import 'package:shared_preferences/shared_preferences.dart';
//import '../../services/file_storage_service.dart';

class HeaderWidget extends StatelessWidget {
  const HeaderWidget({Key? key}) : super(key: key);

  Future<Map<String, dynamic>> _loadUserData() async {
    // Initialize file storage service
    //final fileStorageService = await FileStorageService.getInstance();

    // Get login state to retrieve user ID
    final loginState = null; //await fileStorageService.getLoginState();
    if (loginState == null) {
      return {
        'name': 'Guest',
        'profileImage': 'assets/icons/patient_icon.png',
        'day': 1,
      };
    }

    final userId = loginState['user_id'];
    final userRole = loginState['role'];

    // Get user profile data
    final userProfile = null; //await fileStorageService.getUserProfile(userId);
    if (userProfile == null) {
      return {
        'name': 'Guest',
        'profileImage': 'assets/images/default_avatar.png',
        'day': 1,
      };
    }

    // Get treatment data to calculate day count
    int dayCount = 1;
    if (userRole == 'patient') {
      final treatments =
          null; // await fileStorageService.getTreatments(userId);
      if (treatments.isNotEmpty) {
        // Sort treatments by start date to get the most recent one
        treatments.sort((a, b) => b.startDate.compareTo(a.startDate));
        final latestTreatment = treatments.first;

        final today = DateTime.now();
        dayCount =
            today.difference(latestTreatment.startDate).inDays +
            1; // +1 because day 1 is the start day
      }
    }

    return {
      'name': '${userProfile.firstName} ${userProfile.lastName}',
      'profileImage': 'assets/images/default_avatar.png', // Default image
      'day': dayCount,
    };
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Map<String, dynamic>>(
      future: _loadUserData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        final userData =
            snapshot.data ??
            {
              'name': 'Guest',
              'profileImage': 'assets/images/default_avatar.png',
              'day': 1,
            };

        return Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundImage: AssetImage(userData['profileImage']),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Day ${userData['day']}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).hintColor,
                  ),
                ),
                Text(
                  'Welcome ${userData['name']}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const Spacer(),
            IconButton(
              icon: const Icon(Icons.notifications_none_outlined, size: 28),
              onPressed: () {},
            ),
          ],
        );
      },
    );
  }
}
