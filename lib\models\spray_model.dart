/// Spray model class for storing and managing Medicament information
/// Designed for future Supabase integration
class SprayModel {
  /// Required fields
  final String name;
  final double nbrDoses;
  final double doseType;
  /// Optional fields with default values
  final String color;

  SprayModel({
    // Required fields
    required this.name,
    required this.nbrDoses,
    required this.doseType,
    // Optional fields with default values
    this.color = 'Unknown',
  });

  /// Demo doctor data for testing and development
  static List<SprayModel> demoDoctors = [
    SprayModel(
      name: 'ERVA',
      nbrDoses: 120,
      color: 'Purple',
      doseType: 250 //mcg

    ),
    SprayModel(
      name: 'AEROL',
      nbrDoses: 200,
      color: 'Blue',
      doseType: 200 //mcg

    ),
  ];
}