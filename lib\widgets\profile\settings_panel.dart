import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:befine/theme/app_theme.dart';

class SettingsPanel extends StatefulWidget {
  final bool notificationsEnabled;
  final bool remindersEnabled;
  final String selectedLanguage;
  final String selectedTheme;
  final int reminderTime;
  final Function(bool) onNotificationsChanged;
  final Function(bool) onRemindersChanged;
  final Function(String) onLanguageChanged;
  final Function(String) onThemeChanged;
  final Function(int) onReminderTimeChanged;

  const SettingsPanel({
    Key? key,
    required this.notificationsEnabled,
    required this.remindersEnabled,
    required this.selectedLanguage,
    required this.selectedTheme,
    required this.reminderTime,
    required this.onNotificationsChanged,
    required this.onRemindersChanged,
    required this.onLanguageChanged,
    required this.onThemeChanged,
    required this.onReminderTimeChanged,
  }) : super(key: key);

  @override
  State<SettingsPanel> createState() => _SettingsPanelState();
}

class _SettingsPanelState extends State<SettingsPanel> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Settings',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
          ),
          const SizedBox(height: 15),
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            color: AppTheme.surfaceColor,
            child: Column(
              children: [
                SwitchListTile(
                  title: Text(
                    'Notifications',
                    style: TextStyle(
                      color: AppTheme.textPrimaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  value: widget.notificationsEnabled,
                  onChanged: widget.onNotificationsChanged,
                  activeColor: AppTheme.primaryColor,
                ),
                SwitchListTile(
                  title: Text(
                    'Reminders',
                    style: TextStyle(
                      color: AppTheme.textPrimaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  value: widget.remindersEnabled,
                  onChanged: widget.onRemindersChanged,
                  activeColor: AppTheme.primaryColor,
                ),
                if (widget.remindersEnabled) 
                  Padding(
                    padding: const EdgeInsets.only(left: 16, right: 16),
                    child: ListTile(
                      title: Text(
                        'Reminder Time',
                        style: TextStyle(
                          color: AppTheme.textPrimaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      subtitle: Text(
                        'Minutes before medication time',
                        style: TextStyle(
                          color: AppTheme.textTertiaryColor,
                          fontSize: 12,
                        ),
                      ),
                      trailing: DropdownButton<int>(
                        value: widget.reminderTime,
                        onChanged: (int? newValue) {
                          if (newValue != null) {
                            widget.onReminderTimeChanged(newValue);
                          }
                        },
                        dropdownColor: AppTheme.surfaceColor,
                        items: <int>[5, 10, 15, 30, 60].map<DropdownMenuItem<int>>((int value) {
                          return DropdownMenuItem<int>(
                            value: value,
                            child: Text(
                              '$value min',
                              style: TextStyle(color: AppTheme.textPrimaryColor),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ListTile(
                  title: Text(
                    'Theme',
                    style: TextStyle(
                      color: AppTheme.textPrimaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  trailing: DropdownButton<String>(
                    value: widget.selectedTheme,
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        widget.onThemeChanged(newValue);
                      }
                    },
                    dropdownColor: AppTheme.surfaceColor,
                    items: <String>['system', 'light', 'dark'].map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(
                          value[0].toUpperCase() + value.substring(1),
                          style: TextStyle(color: AppTheme.textPrimaryColor),
                        ),
                      );
                    }).toList(),
                  ),
                ),
                ListTile(
                  title: Text(
                    'Language',
                    style: TextStyle(
                      color: AppTheme.textPrimaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  trailing: DropdownButton<String>(
                    value: widget.selectedLanguage,
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        widget.onLanguageChanged(newValue);
                      }
                    },
                    dropdownColor: AppTheme.surfaceColor,
                    items: <String>['English', 'French', 'Arabic'].map<DropdownMenuItem<String>>((String value) {
                      return DropdownMenuItem<String>(
                        value: value,
                        child: Text(
                          value,
                          style: TextStyle(color: AppTheme.textPrimaryColor),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
