import 'package:flutter/material.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:befine/widgets/home/<USER>';
import 'package:befine/widgets/home/<USER>';
import 'package:befine/widgets/home/<USER>';
import 'package:befine/widgets/home/<USER>';
import 'package:befine/widgets/home/<USER>';
import 'package:befine/widgets/home/<USER>';
import 'package:befine/widgets/home/<USER>';
import 'package:befine/widgets/home/<USER>';

class PatientHomePage extends StatelessWidget {
  const PatientHomePage({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                const HeaderWidget(),
                const SizedBox(height: 16),
                const MoodTrackerWidget(),
                const SizedBox(height: 16),
                const ActionButtonsWidget(),
                const SizedBox(height: 24),
                GestureDetector(
                  onTap: () {
                    // TODO : Navigate to summary page
                  },
                  child: const SummaryWidget(),
                ),
                const SizedBox(height: 16),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        children: const [
                          MedicationReminderWidget(),
                          SizedBox(height: 16),
                          TreatmentWidget(),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ConstrainedBox(
                        constraints: BoxConstraints(maxHeight: 375),
                        child: const DeviceWidget(),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                const WeatherWidget(),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
