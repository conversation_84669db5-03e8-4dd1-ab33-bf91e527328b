/// Connection type enum for device connectivity
enum ConnectionType { none, ble, wifi }

/// Battery status enum for device battery level
enum BatteryStatus { unknown, low, medium, high, full }

/// Storage status enum for device storage capacity
enum StorageStatus { unknown, ok, warning, full }

/// BLE service and characteristic UUIDs for ESP32C6 devices
class BleUuids {
  /// Main service UUID for SmartInhealer device
  static const String serviceUuid = "4fafc201-1fb5-459e-8fcc-c5c9c331914b";

  /// Data characteristic UUID for sending/receiving measurement data
  static const String dataCharacteristicUuid =
      "beb5483e-36e1-4688-b7f5-ea07361b26a8";

  /// Status characteristic UUID for device status updates
  static const String statusCharacteristicUuid =
      "beb5483e-36e1-4688-b7f5-ea07361b26a9";
}

/// Device model class for SmartInhealer IoT devices
/// Designed for BLE and WiFi connectivity with ESP32C6
class Device {
  /// Unique device identifier
  final String? deviceId;

  /// Device type (e.g., "SmartInhealer")
  final String? deviceType;

  /// User-defined name for the device
  final String? customName;

  /// BLE MAC address for the device (used for reconnection)
  final String? bleMacAddress;

  /// Battery level (0-100)
  final int? batteryLevel;

  /// Storage status of the device
  final StorageStatus? storageStatus;

  /// Whether the device is currently connected
  final bool? isConnected;

  /// Type of connection (none, BLE, WiFi)
  final ConnectionType? connectionType;

  /// Last time device synced data
  final DateTime? lastSync;

  /// Device registration timestamp
  final DateTime? createdAt;

  /// Last update timestamp
  final DateTime? updatedAt;

  /// Constructor for Device model
  Device({
    this.deviceId,
    this.deviceType,
    this.customName,
    this.bleMacAddress,
    this.batteryLevel,
    this.storageStatus,
    this.isConnected,
    this.connectionType,
    this.lastSync,
    this.createdAt,
    this.updatedAt,
  });

  /// Create a copy of this device with some fields updated
  Device copyWith({
    String? deviceId,
    String? deviceType,
    String? customName,
    String? bleMacAddress,
    int? batteryLevel,
    StorageStatus? storageStatus,
    bool? isConnected,
    ConnectionType? connectionType,
    DateTime? lastSync,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Device(
      deviceId: deviceId ?? this.deviceId,
      deviceType: deviceType ?? this.deviceType,
      customName: customName ?? this.customName,
      bleMacAddress: bleMacAddress ?? this.bleMacAddress,
      batteryLevel: batteryLevel ?? this.batteryLevel,
      storageStatus: storageStatus ?? this.storageStatus,
      isConnected: isConnected ?? this.isConnected,
      connectionType: connectionType ?? this.connectionType,
      lastSync: lastSync ?? this.lastSync,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Utility method to derive battery status from level
  static BatteryStatus getBatteryStatus(int level) {
    if (level < 0) return BatteryStatus.unknown;
    if (level <= 10) return BatteryStatus.low;
    if (level <= 40) return BatteryStatus.medium;
    if (level <= 80) return BatteryStatus.high;
    return BatteryStatus.full;
  }

  /// String representation of the device
  @override
  String toString() {
    return 'Device(id: ${deviceId ?? "unknown"}, '
        'type: ${deviceType ?? "unknown"}, '
        'name: "${customName ?? "unnamed"}", '
        'connected: ${isConnected ?? false} via ${connectionType?.toString().split('.').last ?? "none"}, '
        'battery: ${batteryLevel ?? 0}%, '
        'storage: ${storageStatus?.toString().split('.').last ?? "unknown"}, '
        'BLE: ${bleMacAddress ?? "unknown MAC"})';
  }
}
