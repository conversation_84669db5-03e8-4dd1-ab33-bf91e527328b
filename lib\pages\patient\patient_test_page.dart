import 'package:flutter/material.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:befine/models/measurement_data.dart';
import 'package:befine/models/test_results.dart';
import 'package:befine/services/ble_manager.dart';
import 'package:befine/services/ble_test_service.dart';
import 'package:befine/widgets/device_info_widget.dart';
import 'package:befine/widgets/expandable_chart_card.dart';
import 'package:befine/widgets/flow_chart.dart';
import 'package:befine/widgets/flow_volume_chart.dart';
import 'package:befine/widgets/test_control_widget.dart';
import 'package:befine/widgets/test_results_card.dart';
import 'package:befine/widgets/volume_chart.dart';
import 'package:provider/provider.dart';

class PatientTestPage extends StatefulWidget {
  const PatientTestPage({Key? key}) : super(key: key);

  @override
  State<PatientTestPage> createState() => _PatientTestPageState();
}

class _PatientTestPageState extends State<PatientTestPage> {
  late BleTestService _bleTestService;

  @override
  void initState() {
    super.initState();
    // Initialize BLE test service
    _bleTestService = BleTestService(
      Provider.of<BleManager>(context, listen: false),
    );

    // Check if device is connected and automatically set up test
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkDeviceConnection();
    });
  }

  @override
  void dispose() {
    _bleTestService.dispose();
    super.dispose();
  }

  // Check if a device is connected and setup test service
  void _checkDeviceConnection() async {
    final bleManager = Provider.of<BleManager>(context, listen: false);

    if (bleManager.connectionState != BleConnectionState.connected) {
      // Show a snackbar suggesting to connect a device first
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Connectez un appareil pour effectuer un test'),
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Gérer les appareils',
            onPressed: () {
              // Navigate to device management screen
              Navigator.of(context).pushNamed('/manage-devices');
            },
          ),
        ),
      );
    } else {
      // Device is connected, setup the test service
      await _bleTestService.setupCharacteristics();

      // Get initial device status
      await _bleTestService.getDeviceStatus();

      // Show welcome message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Appareil connecté. Prêt à démarrer le test.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _bleTestService,
      child: Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        appBar: AppBar(
          title: const Text('Test Respiratoire'),
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
        ),
        body: _buildContent(),
      ),
    );
  }

  Widget _buildContent() {
    return Consumer2<BleManager, BleTestService>(
      builder: (context, bleManager, bleTestService, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Device info widget
              const DeviceInfoWidget(),
              const SizedBox(height: 24),

              // Test control widget
              if (bleManager.connectionState == BleConnectionState.connected)
                const TestControlWidget(),

              const SizedBox(height: 24),

              // Charts
              _buildCharts(bleTestService.measurementData, bleTestService),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCharts(
    MeasurementData measurementData,
    BleTestService bleTestService,
  ) {
    // Get test status to check if test is complete
    final isTestComplete =
        bleTestService.testStatus == "complete" ||
        bleTestService.testStatus == "stopped";

    // Calculate test results if test is complete and we have data
    final hasData =
        measurementData.flowData.isNotEmpty &&
        measurementData.volumeData.isNotEmpty;
    final TestResults testResults =
        hasData ? measurementData.calculateResults() : TestResults.empty();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main Flow/Volume chart (always visible)
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: FlowVolumeChart(
            flowData: measurementData.flowData,
            volumeData: measurementData.volumeData,
          ),
        ),

        // Test Results Card (only shown when test is complete)
        if (isTestComplete && hasData)
          Column(
            children: [
              const SizedBox(height: 16),
              TestResultsCard(
                results: testResults,
                measurementData: measurementData,
              ),
            ],
          ),

        const SizedBox(height: 16),

        // Expandable Flow chart
        ExpandableChartCard(
          title: 'Courbe Débit',
          chart: FlowChart(flowData: measurementData.flowData),
        ),

        // Expandable Volume chart
        ExpandableChartCard(
          title: 'Courbe Volume',
          chart: VolumeChart(volumeData: measurementData.volumeData),
        ),
      ],
    );
  }
}
