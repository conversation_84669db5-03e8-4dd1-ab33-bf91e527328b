import 'package:flutter/material.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:befine/models/measurement_data.dart';
import 'package:befine/models/test_results.dart';
import 'package:befine/services/ble_manager.dart';
import 'package:befine/services/ble_test_service.dart';
import 'package:befine/widgets/ble_connection_widget.dart';
import 'package:befine/widgets/expandable_chart_card.dart';
import 'package:befine/widgets/flow_chart.dart';
import 'package:befine/widgets/flow_volume_chart.dart';
import 'package:befine/widgets/test_results_card.dart';
import 'package:befine/widgets/volume_chart.dart';
import 'package:provider/provider.dart';

class PatientTestPage extends StatefulWidget {
  const PatientTestPage({Key? key}) : super(key: key);

  @override
  State<PatientTestPage> createState() => _PatientTestPageState();
}

class _PatientTestPageState extends State<PatientTestPage> {
  late BleTestService _bleTestService;

  @override
  void initState() {
    super.initState();
    // Initialize BLE test service
    _bleTestService = BleTestService(
      Provider.of<BleManager>(context, listen: false),
    );

    // Check if device is connected and automatically set up test
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkDeviceConnection();
    });
  }

  @override
  void dispose() {
    _bleTestService.dispose();
    super.dispose();
  }

  // Check if a device is connected and set up the test
  void _checkDeviceConnection() async {
    final bleManager = Provider.of<BleManager>(context, listen: false);

    if (bleManager.connectionState != BleConnectionState.connected) {
      // Show a snackbar suggesting to connect a device first
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Connectez un appareil pour effectuer un test'),
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Gérer les appareils',
            onPressed: () {
              // Navigate to device management screen
              Navigator.of(context).pushNamed('/manage-devices');
            },
          ),
        ),
      );
    } else {
      // Device is connected, show a welcome message and auto-start the test
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Appareil connecté. Démarrage automatique du test...'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );

      // Auto-start the test after a short delay
      await Future.delayed(const Duration(seconds: 1));

      // Check if widget is still mounted before proceeding
      if (!mounted) return;

      // Try to auto-start the measurement
      final success = await _bleTestService.autoStartMeasurement();

      if (success) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Test démarré automatiquement'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      } else {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Impossible de démarrer le test automatiquement',
            ),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 3),
            action: SnackBarAction(
              label: 'Démarrer',
              onPressed: () => _startMeasurementCycle(context, _bleTestService),
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _bleTestService,
      child: Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        appBar: AppBar(
          title: const Text('Test Respiratoire'),
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
        ),
        body: _buildContent(),
      ),
    );
  }

  Widget _buildContent() {
    return Consumer2<BleManager, BleTestService>(
      builder: (context, bleManager, bleTestService, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // BLE connection widget
              const BleConnectionWidget(),
              const SizedBox(height: 24),

              // Start measurement button
              if (bleManager.connectionState == BleConnectionState.connected)
                _buildStartMeasurementButton(context, bleTestService),

              const SizedBox(height: 24),

              // Charts
              _buildCharts(bleTestService.measurementData, bleTestService),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStartMeasurementButton(
    BuildContext context,
    BleTestService bleTestService,
  ) {
    // Get test status
    final testStatus = bleTestService.testStatus;
    final isMeasuring = testStatus == "measuring";
    final isComplete = testStatus == "complete" || testStatus == "stopped";
    final progress = bleTestService.testProgress;
    final currentSample = bleTestService.currentSample;
    final maxSamples = bleTestService.maxSamples;

    return Column(
      children: [
        // Progress indicator
        if (isMeasuring)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Column(
              children: [
                LinearProgressIndicator(
                  value: progress / 100.0,
                  backgroundColor: Colors.grey[200],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.primaryColor,
                  ),
                  minHeight: 10,
                  borderRadius: BorderRadius.circular(5),
                ),
                const SizedBox(height: 8),
                Text(
                  'Échantillon: $currentSample / $maxSamples (${progress.toStringAsFixed(1)}%)',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),

        // Status indicator
        if (isComplete)
          Container(
            padding: const EdgeInsets.all(8.0),
            margin: const EdgeInsets.only(bottom: 16.0),
            decoration: BoxDecoration(
              color: Colors.green.withAlpha(25),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green.withAlpha(75)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.check_circle, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Test terminé avec $currentSample échantillons',
                  style: const TextStyle(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

        // Control buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (!isMeasuring)
              ElevatedButton.icon(
                onPressed:
                    () => _startMeasurementCycle(context, bleTestService),
                icon: const Icon(Icons.play_arrow),
                label: const Text('Démarrer'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                  textStyle: const TextStyle(fontSize: 18),
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              )
            else
              ElevatedButton.icon(
                onPressed: () => _stopMeasurementCycle(context, bleTestService),
                icon: const Icon(Icons.stop),
                label: const Text('Arrêter le test'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                  textStyle: const TextStyle(fontSize: 18),
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),

            if (isComplete)
              Padding(
                padding: const EdgeInsets.only(left: 16.0),
                child: ElevatedButton.icon(
                  onPressed:
                      () => _resetMeasurementCycle(context, bleTestService),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Réinitialiser'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 16,
                    ),
                    textStyle: const TextStyle(fontSize: 18),
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildCharts(
    MeasurementData measurementData,
    BleTestService bleTestService,
  ) {
    // Get test status to check if test is complete
    final isTestComplete =
        bleTestService.testStatus == "complete" ||
        bleTestService.testStatus == "stopped";

    // Calculate test results if test is complete and we have data
    final hasData =
        measurementData.flowData.isNotEmpty &&
        measurementData.volumeData.isNotEmpty;
    final TestResults testResults =
        hasData ? measurementData.calculateResults() : TestResults.empty();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main Flow/Volume chart (always visible)
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: FlowVolumeChart(
            flowData: measurementData.flowData,
            volumeData: measurementData.volumeData,
          ),
        ),

        // Test Results Card (only shown when test is complete)
        if (isTestComplete && hasData)
          Column(
            children: [
              const SizedBox(height: 16),
              TestResultsCard(
                results: testResults,
                measurementData: measurementData,
              ),
            ],
          ),

        const SizedBox(height: 16),

        // Expandable Flow chart
        ExpandableChartCard(
          title: 'Courbe Débit',
          chart: FlowChart(flowData: measurementData.flowData),
        ),

        // Expandable Volume chart
        ExpandableChartCard(
          title: 'Courbe Volume',
          chart: VolumeChart(volumeData: measurementData.volumeData),
        ),
      ],
    );
  }

  void _startMeasurementCycle(
    BuildContext context,
    BleTestService bleTestService,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    final success = await bleTestService.startMeasurementCycle();

    if (!success) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Échec du démarrage du test'),
          backgroundColor: Colors.red,
        ),
      );
    } else {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Test démarré'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _stopMeasurementCycle(
    BuildContext context,
    BleTestService bleTestService,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    final success = await bleTestService.stopMeasurementCycle();

    if (!success) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Échec de l\'arrêt du test'),
          backgroundColor: Colors.red,
        ),
      );
    } else {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Test arrêté'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _resetMeasurementCycle(
    BuildContext context,
    BleTestService bleTestService,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    final success = await bleTestService.resetMeasurementCycle();

    if (!success) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Échec de la réinitialisation du test'),
          backgroundColor: Colors.red,
        ),
      );
    } else {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Test réinitialisé avec succès'),
          backgroundColor: AppTheme.primaryColor,
        ),
      );
    }
  }
}
