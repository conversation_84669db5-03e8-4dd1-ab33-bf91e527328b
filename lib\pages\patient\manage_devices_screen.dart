import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:befine/models/device_model.dart';
import 'package:befine/services/ble_manager.dart';
import 'package:befine/services/device_service.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';

class ManageDevicesScreen extends StatefulWidget {
  const ManageDevicesScreen({Key? key}) : super(key: key);

  @override
  State<ManageDevicesScreen> createState() => _ManageDevicesScreenState();
}

class _ManageDevicesScreenState extends State<ManageDevicesScreen> {
  // Scanning state
  bool _isScanning = false;

  // Visibility state for BLE devices section
  bool _showBleDevicesSection = false;

  // Device service will be accessed via Provider

  // BLE devices
  List<BluetoothDevice> _bleDevices = [];
  List<BluetoothDevice> _filteredBleDevices =
      []; // Filtered list for ESP32 devices
  StreamSubscription<List<ScanResult>>? _scanResultsSubscription;

  @override
  void initState() {
    super.initState();
    // Initialize BLE
    _initBle();
    // Initialize DeviceService and set up auto-connection
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final deviceService = Provider.of<DeviceService>(context, listen: false);
      final bleManager = Provider.of<BleManager>(context, listen: false);

      deviceService.initialize().then((_) {
        // Set up auto-connection after device service is initialized
        bleManager.setDeviceService(deviceService);
      });
    });
  }

  @override
  void dispose() {
    // Cancel scan subscription
    _scanResultsSubscription?.cancel();

    // We don't disconnect the device when navigating away
    // This allows the connection to persist between screens

    super.dispose();
  }

  // Initialize BLE
  Future<void> _initBle() async {
    try {
      // Set log level for debugging
      FlutterBluePlus.setLogLevel(LogLevel.verbose, color: true);

      // Check if Bluetooth is supported
      if (await FlutterBluePlus.isSupported == false) {
        _showErrorDialog("Bluetooth not supported on this device");
        return;
      }
    } catch (e) {
      debugPrint('Bluetooth initialization error: $e');
    }
  }

  // Start scanning for BLE devices
  Future<void> _startScan() async {
    if (_isScanning) return;

    try {
      // Set scanning state to true and show BLE devices section
      setState(() {
        _isScanning = true;
        _showBleDevicesSection = true;
        _bleDevices = [];
        _filteredBleDevices = [];
      });

      // Request permissions first
      bool permissionsGranted = await _requestPermissions();
      if (!permissionsGranted) {
        debugPrint('Permissions not granted, cannot scan');
        setState(() => _isScanning = false);
        return;
      }

      debugPrint('All permissions granted, proceeding with scan');

      // Make sure Bluetooth is on
      if (FlutterBluePlus.adapterStateNow != BluetoothAdapterState.on) {
        debugPrint('Bluetooth is not on, requesting to turn it on');
        bool bluetoothTurnedOn = await _requestBluetoothPermission();
        if (!bluetoothTurnedOn) {
          debugPrint('Failed to turn on Bluetooth');
          setState(() => _isScanning = false);
          _showErrorDialog("Please turn on Bluetooth to scan for devices");
          return;
        }
      }

      // Cancel any existing subscription
      if (_scanResultsSubscription != null) {
        await _scanResultsSubscription!.cancel();
        _scanResultsSubscription = null;
      }

      // Listen for scan results
      _scanResultsSubscription = FlutterBluePlus.scanResults.listen(
        (results) {
          // Update the list of discovered devices
          if (mounted) {
            setState(() {
              // Create fresh lists for this scan
              List<BluetoothDevice> newBleDevices = [];
              List<BluetoothDevice> newFilteredDevices = [];

              for (ScanResult result in results) {
                // Add to all devices list (avoid duplicates)
                bool deviceExists = newBleDevices.any(
                  (device) => device.remoteId == result.device.remoteId,
                );

                if (!deviceExists) {
                  newBleDevices.add(result.device);

                  // Log detailed device info
                  debugPrint('=== Found BLE Device ===');
                  debugPrint('Name: "${result.device.platformName}"');
                  debugPrint('ID: ${result.device.remoteId}');
                  debugPrint('RSSI: ${result.rssi}');
                  debugPrint(
                    'Service UUIDs: ${result.advertisementData.serviceUuids}',
                  );
                  debugPrint(
                    'Manufacturer Data: ${result.advertisementData.manufacturerData}',
                  );

                  // Improved filtering for SmartInhealer devices
                  if (_isSmartInhalerDevice(result)) {
                    newFilteredDevices.add(result.device);
                    debugPrint('✓ Device identified as SmartInhealer/ESP32');
                  } else {
                    debugPrint('✗ Device filtered out');
                  }
                }
              }

              // Update the lists
              _bleDevices = newBleDevices;
              _filteredBleDevices = newFilteredDevices;

              debugPrint(
                'Scan update: ${_bleDevices.length} total devices, ${_filteredBleDevices.length} filtered devices',
              );
            });
          }
        },
        onError: (e) {
          debugPrint('Error during scan: $e');
          if (mounted) {
            setState(() => _isScanning = false);
            _showErrorDialog("Error during scan: $e");
          }
        },
      );

      // Force stop any existing scan first
      if (await FlutterBluePlus.isScanning.first) {
        await FlutterBluePlus.stopScan();
        await Future.delayed(const Duration(milliseconds: 500));
      }

      debugPrint('Starting fresh BLE scan...');
      // Start scanning with increased timeout
      await FlutterBluePlus.startScan(
        timeout: const Duration(seconds: 20),
        androidScanMode: AndroidScanMode.lowLatency,
      );
      debugPrint('✅ BLE scan started successfully');

      // Wait for scan to complete
      await FlutterBluePlus.isScanning.where((val) => val == false).first;
      debugPrint('🏁 Scan completed');

      if (mounted) {
        setState(() => _isScanning = false);
      }
    } catch (e) {
      debugPrint('❌ Error during scan: $e');
      if (mounted) {
        setState(() => _isScanning = false);
        _showErrorDialog("Scan failed: $e");
      }
    }
  }

  // Request all necessary permissions
  Future<bool> _requestPermissions() async {
    if (Platform.isAndroid) {
      // First focus on location permission as it's critical for BLE scanning
      bool locationGranted = await Permission.location.isGranted;
      bool locationPermissionDetermined = true;

      debugPrint('Initial location permission status: $locationGranted');

      // If location permission is not granted, request it first
      if (!locationGranted) {
        PermissionStatus locationStatus = await Permission.location.request();
        debugPrint('Location permission request result: $locationStatus');

        // Check the result of the location permission request
        if (locationStatus.isPermanentlyDenied) {
          debugPrint('Location permission permanently denied');
          locationPermissionDetermined = false;

          // Show a dialog explaining why location is needed and how to enable it
          if (mounted) {
            await showDialog(
              context: context,
              barrierDismissible: false,
              builder:
                  (context) => AlertDialog(
                    title: Text(
                      'Location Permission Required',
                      style: TextStyle(color: AppTheme.textPrimaryColor),
                    ),
                    content: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Location permission is required for Bluetooth scanning on Android devices.',
                          style: TextStyle(color: AppTheme.textSecondaryColor),
                        ),
                        SizedBox(height: 10),
                        Text(
                          'Please enable location permission in your device settings:',
                          style: TextStyle(color: AppTheme.textSecondaryColor),
                        ),
                        SizedBox(height: 10),
                        Text(
                          '1. Go to Settings > Apps > BeFine > Permissions',
                          style: TextStyle(color: AppTheme.textSecondaryColor),
                        ),
                        Text(
                          '2. Tap on Location',
                          style: TextStyle(color: AppTheme.textSecondaryColor),
                        ),
                        Text(
                          '3. Select "Allow"',
                          style: TextStyle(color: AppTheme.textSecondaryColor),
                        ),
                      ],
                    ),
                    actions: [
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                          // Open app settings
                          openAppSettings();
                        },
                        style: TextButton.styleFrom(
                          foregroundColor: AppTheme.primaryColor,
                        ),
                        child: Text('Open Settings'),
                      ),
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          foregroundColor: AppTheme.errorColor,
                        ),
                        child: Text('Cancel'),
                      ),
                    ],
                  ),
            );
          }
          return false;
        } else if (locationStatus.isDenied) {
          debugPrint('Location permission denied');
          return false;
        } else {
          locationGranted = locationStatus.isGranted;
        }
      }

      // If location permission is determined and granted, proceed with Bluetooth permissions
      if (locationPermissionDetermined && locationGranted) {
        // Now check Bluetooth permissions
        bool bluetoothScanGranted = await Permission.bluetoothScan.isGranted;
        bool bluetoothConnectGranted =
            await Permission.bluetoothConnect.isGranted;

        debugPrint('Bluetooth permissions status:');
        debugPrint('- Bluetooth Scan: $bluetoothScanGranted');
        debugPrint('- Bluetooth Connect: $bluetoothConnectGranted');

        // If all permissions are already granted, return true
        if (bluetoothScanGranted && bluetoothConnectGranted) {
          debugPrint('All permissions already granted');
          return true;
        }

        // Request Bluetooth permissions
        if (!bluetoothScanGranted) {
          PermissionStatus status = await Permission.bluetoothScan.request();
          debugPrint('Bluetooth Scan permission request result: $status');
          bluetoothScanGranted = status.isGranted;
        }

        if (!bluetoothConnectGranted) {
          PermissionStatus status = await Permission.bluetoothConnect.request();
          debugPrint('Bluetooth Connect permission request result: $status');
          bluetoothConnectGranted = status.isGranted;
        }

        // Check if all permissions are now granted
        bool allGranted = bluetoothScanGranted && bluetoothConnectGranted;

        // If Bluetooth permissions are still not granted, show instructions
        if (!allGranted) {
          debugPrint('Not all Bluetooth permissions were granted');

          if (mounted) {
            showDialog(
              context: context,
              barrierDismissible: false,
              builder:
                  (context) => AlertDialog(
                    title: Text(
                      'Bluetooth Permissions Required',
                      style: TextStyle(color: AppTheme.textPrimaryColor),
                    ),
                    content: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Please enable the following permissions in your device settings:',
                          style: TextStyle(color: AppTheme.textSecondaryColor),
                        ),
                        SizedBox(height: 10),
                        if (!bluetoothScanGranted)
                          Text(
                            '• Bluetooth Scan',
                            style: TextStyle(
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        if (!bluetoothConnectGranted)
                          Text(
                            '• Bluetooth Connect',
                            style: TextStyle(
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                      ],
                    ),
                    actions: [
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                          openAppSettings();
                        },
                        style: TextButton.styleFrom(
                          foregroundColor: AppTheme.primaryColor,
                        ),
                        child: Text('Open Settings'),
                      ),
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          foregroundColor: AppTheme.errorColor,
                        ),
                        child: Text('Cancel'),
                      ),
                    ],
                  ),
            );
          }
        }

        return allGranted;
      }

      return false;
    }

    // For iOS, permissions are handled differently
    if (Platform.isIOS) {
      // On iOS, we need to request Bluetooth permission
      bool bluetoothGranted = await Permission.bluetooth.isGranted;
      if (!bluetoothGranted) {
        PermissionStatus status = await Permission.bluetooth.request();
        bluetoothGranted = status.isGranted;
      }

      return bluetoothGranted;
    }

    return true;
  }

  // Stop scanning
  void _stopScan() async {
    if (!_isScanning) return;

    try {
      await FlutterBluePlus.stopScan();
      setState(() => _isScanning = false);
      // Note: We keep _showBleDevicesSection = true to keep the section visible
    } catch (e) {
      debugPrint('Error stopping scan: $e');
    }
  }

  // Clear scan results
  void _clearScanResults() {
    setState(() {
      _bleDevices = [];
      _filteredBleDevices = [];
      _showBleDevicesSection = false;
    });
    debugPrint('Scan results cleared');
  }

  // Request Bluetooth permission
  Future<bool> _requestBluetoothPermission() async {
    try {
      // Turn on Bluetooth
      await FlutterBluePlus.turnOn();
      return true;
    } catch (e) {
      debugPrint('Failed to turn on Bluetooth: $e');
      _showErrorDialog("Please enable Bluetooth to scan for devices");
      return false;
    }
  }

  // Connect to a BLE device
  Future<void> _connectToDevice(BluetoothDevice device) async {
    final bleManager = Provider.of<BleManager>(context, listen: false);

    try {
      debugPrint('=== Starting connection to device ===');
      debugPrint('Device ID: ${device.remoteId}');
      debugPrint('Device Name: ${device.platformName}');
      debugPrint('Device is connected: ${device.isConnected}');
      debugPrint('BLE Manager state: ${bleManager.connectionState}');

      // Show connecting dialog
      _showConnectingDialog(device);

      // Check Bluetooth state first
      if (FlutterBluePlus.adapterStateNow != BluetoothAdapterState.on) {
        throw Exception('Bluetooth is not enabled');
      }

      debugPrint('Bluetooth is enabled, attempting connection...');

      // Connect to the device using BleManager
      final success = await bleManager.connectToDevice(device);

      debugPrint('Connection result: $success');
      debugPrint(
        'BLE Manager state after connection: ${bleManager.connectionState}',
      );

      // Hide connecting dialog
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Hide BLE devices section
      setState(() {
        _showBleDevicesSection = false;
      });

      if (success) {
        // Create basic device info without complex service discovery
        final deviceInfo = {
          'name':
              device.platformName.isNotEmpty
                  ? device.platformName
                  : 'SmartInhealer Device',
          'id': device.remoteId.str,
          'deviceType': 'SmartInhealer',
          'macAddress': device.remoteId.str,
          'batteryLevel': null, // Will be updated later
        };

        // Add device to list and show success
        _addConnectedDeviceToList(deviceInfo);
        _showSuccessDialog(deviceInfo);

        debugPrint('Successfully connected to device: ${device.remoteId}');
      } else {
        debugPrint('Connection failed - BLE Manager returned false');
        _showDetailedErrorDialog(
          "Connection Failed",
          "Failed to establish BLE connection to the device.",
          "The device may be out of range, already connected to another app, or experiencing interference.",
        );
      }
    } catch (e) {
      debugPrint('Exception during connection: $e');

      // Hide connecting dialog and BLE devices section
      if (mounted) {
        Navigator.of(context).pop();
        setState(() {
          _showBleDevicesSection = false;
        });

        _showDetailedErrorDialog(
          "Connection Error",
          "An error occurred while connecting to the device.",
          "Error details: $e",
        );
      }
    }
  }

  // Check if a device is a SmartInhealer device
  bool _isSmartInhalerDevice(ScanResult result) {
    String deviceName = result.device.platformName.toLowerCase().trim();
    String localName = result.advertisementData.advName.toLowerCase().trim();

    debugPrint('🔍 DEVICE ANALYSIS:');
    debugPrint('   Platform Name: "$deviceName"');
    debugPrint('   Local Name: "$localName"');
    debugPrint('   RSSI: ${result.rssi} dBm');
    debugPrint('   Service UUIDs: ${result.advertisementData.serviceUuids}');
    debugPrint(
      '   Manufacturer Data: ${result.advertisementData.manufacturerData}',
    );

    // 1. Check for explicit SmartInhealer name (highest priority)
    if (deviceName.contains('smartinhaler') ||
        localName.contains('smartinhaler')) {
      debugPrint('✅ MATCH: SmartInhealer name found');
      return true;
    }

    // 2. Check for ESP32 in name
    if (deviceName.contains('esp32') ||
        deviceName.contains('esp') ||
        localName.contains('esp32') ||
        localName.contains('esp')) {
      debugPrint('✅ MATCH: ESP32 name found');
      return true;
    }

    // 3. Check for our specific service UUID (most reliable)
    if (result.advertisementData.serviceUuids.isNotEmpty) {
      for (Guid uuid in result.advertisementData.serviceUuids) {
        String uuidStr = uuid.toString().toLowerCase();
        debugPrint('   Checking UUID: $uuidStr');

        // Check for our SmartInhealer service UUID
        if (uuidStr == '4fafc201-1fb5-459e-8fcc-c5c9c331914b' ||
            uuidStr.contains('4fafc201')) {
          debugPrint('✅ MATCH: SmartInhealer service UUID found!');
          return true;
        }
      }
    }

    // 4. Check manufacturer data for ESP32 characteristics
    if (result.advertisementData.manufacturerData.isNotEmpty) {
      // ESP32 often uses manufacturer ID 0x02E5 (Espressif)
      if (result.advertisementData.manufacturerData.containsKey(0x02E5)) {
        debugPrint('✅ MATCH: Espressif manufacturer ID found');
        return true;
      }

      // For unnamed devices with manufacturer data and decent signal
      if ((deviceName.isEmpty || deviceName == 'unknown') &&
          result.rssi > -60) {
        debugPrint(
          '✅ MATCH: Unnamed device with manufacturer data and good signal',
        );
        return true;
      }
    }

    // 5. Last resort: very strong signal from unnamed device (likely ESP32 nearby)
    if ((deviceName.isEmpty || deviceName == 'unknown') && result.rssi > -35) {
      debugPrint('✅ MATCH: Very strong signal from unnamed device');
      return true;
    }

    debugPrint('❌ NO MATCH: Device filtered out');
    return false;
  }

  // Add connected device to the list
  void _addConnectedDeviceToList(Map<String, dynamic> deviceInfo) {
    final deviceService = Provider.of<DeviceService>(context, listen: false);

    final int batteryLevel = deviceInfo['batteryLevel'] ?? 0;
    final newDevice = Device(
      deviceId: deviceInfo['id'],
      deviceType: deviceInfo['deviceType'] ?? 'SmartInhealer',
      batteryLevel: batteryLevel,
      storageStatus: StorageStatus.ok,
      lastSync: DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isConnected: true,
      connectionType: ConnectionType.ble,
      customName: deviceInfo['name'] ?? 'SmartInhealer Device',
      bleMacAddress: deviceInfo['macAddress'] ?? deviceInfo['id'],
    );

    // Add or update device using the service
    deviceService.addOrUpdateDevice(newDevice);
  }

  // Show error dialog
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.surfaceColor,
            title: Text('Error', style: TextStyle(color: AppTheme.errorColor)),
            content: Text(
              message,
              style: TextStyle(color: AppTheme.textSecondaryColor),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.primaryColor,
                ),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  // Show detailed error dialog with more information
  void _showDetailedErrorDialog(String title, String message, String details) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.surfaceColor,
            title: Text(title, style: TextStyle(color: AppTheme.errorColor)),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  message,
                  style: TextStyle(color: AppTheme.textSecondaryColor),
                ),
                const SizedBox(height: 12),
                const Text(
                  'Troubleshooting:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                const Text('• Make sure the device is powered on'),
                const Text('• Check if the device is nearby (within 10m)'),
                const Text('• Ensure no other app is connected to the device'),
                const Text('• Try turning Bluetooth off and on'),
                const SizedBox(height: 12),
                ExpansionTile(
                  title: const Text(
                    'Technical Details',
                    style: TextStyle(fontSize: 14),
                  ),
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        details,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.primaryColor,
                ),
                child: const Text('OK'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  // Restart scanning
                  _startScan();
                },
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.primaryColor,
                ),
                child: const Text('Try Again'),
              ),
            ],
          ),
    );
  }

  // Show connecting dialog
  void _showConnectingDialog(BluetoothDevice device) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.surfaceColor,
            title: Text(
              'Connecting',
              style: TextStyle(color: AppTheme.textPrimaryColor),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  'Connecting to ${device.platformName}...',
                  style: TextStyle(color: AppTheme.textSecondaryColor),
                ),
              ],
            ),
          ),
    );
  }

  // Show success dialog
  void _showSuccessDialog(Map<String, dynamic> deviceInfo) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.surfaceColor,
            title: Text(
              'Device Connected',
              style: TextStyle(color: AppTheme.primaryColor),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Successfully connected to ESP32 device:',
                  style: TextStyle(color: AppTheme.textSecondaryColor),
                ),
                const SizedBox(height: 8),
                Text(
                  'Name: ${deviceInfo['name']}',
                  style: TextStyle(color: AppTheme.textPrimaryColor),
                ),
                Text(
                  'ID: ${deviceInfo['id']}',
                  style: TextStyle(color: AppTheme.textPrimaryColor),
                ),
                Text(
                  'Battery: ${deviceInfo['batteryLevel'] != null ? "${deviceInfo['batteryLevel']}%" : "Unknown"} [${Device.getBatteryStatus(deviceInfo['batteryLevel'] ?? 0).toString().split('.').last}]',
                  style: TextStyle(color: AppTheme.textPrimaryColor),
                ),
                Text(
                  'Connection: BLE',
                  style: TextStyle(color: AppTheme.textPrimaryColor),
                ),
                Text(
                  'Storage: OK',
                  style: TextStyle(color: AppTheme.textPrimaryColor),
                ),
                Text(
                  'MAC: ${deviceInfo['macAddress'] ?? "Unknown"}',
                  style: TextStyle(color: AppTheme.textPrimaryColor),
                ),
                Text(
                  'Service: ${BleUuids.serviceUuid.substring(0, 8)}...',
                  style: TextStyle(color: AppTheme.textPrimaryColor),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.primaryColor,
                ),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('My Devices'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelpDialog,
          ),
        ],
      ),
      body: Consumer<DeviceService>(
        builder: (context, deviceService, child) {
          return Column(
            children: [
              _buildDeviceHeader(deviceService),
              Expanded(
                child:
                    deviceService.devices.isEmpty
                        ? _buildEmptyState()
                        : _buildDeviceList(deviceService),
              ),
              // BLE Devices Section - Only show when scanning or devices found
              AnimatedOpacity(
                opacity: _showBleDevicesSection ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 300),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  height: _showBleDevicesSection ? null : 0,
                  child:
                      _showBleDevicesSection
                          ? _buildBleDevicesSection()
                          : const SizedBox(),
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _isScanning ? _stopScan : _startScan,
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        child: Icon(_isScanning ? Icons.stop : Icons.bluetooth_searching),
      ),
    );
  }

  // Build BLE devices section with animation
  Widget _buildBleDevicesSection() {
    // Use filtered devices if available, otherwise use all devices
    final devicesToShow =
        _filteredBleDevices.isNotEmpty ? _filteredBleDevices : _bleDevices;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 opacity
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  _filteredBleDevices.isNotEmpty
                      ? 'SmartInhealer Devices (${_filteredBleDevices.length})'
                      : 'Discovered BLE Devices (${_bleDevices.length})',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (!_isScanning &&
                      (_bleDevices.isNotEmpty ||
                          _filteredBleDevices.isNotEmpty))
                    TextButton.icon(
                      onPressed: _clearScanResults,
                      icon: const Icon(Icons.clear, size: 16),
                      label: const Text(
                        'Clear',
                        style: TextStyle(fontSize: 12),
                      ),
                      style: TextButton.styleFrom(
                        foregroundColor: AppTheme.textSecondaryColor,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        minimumSize: const Size(0, 0),
                      ),
                    ),
                  if (_isScanning) ...[
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Scanning...',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (devicesToShow.isEmpty && _isScanning)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: Center(
                child: Text(
                  'Searching for ESP32 devices...',
                  style: TextStyle(color: AppTheme.textSecondaryColor),
                ),
              ),
            )
          else if (devicesToShow.isEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: Center(
                child: Text(
                  'No ESP32 devices found',
                  style: TextStyle(color: AppTheme.textSecondaryColor),
                ),
              ),
            )
          else
            SizedBox(
              height: 200, // Increased height for better visibility
              child: ListView.builder(
                itemCount: devicesToShow.length,
                itemBuilder: (context, index) {
                  final device = devicesToShow[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    elevation: 0,
                    color: AppTheme.surfaceColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                      side: BorderSide(
                        color: AppTheme.primaryColor.withAlpha(
                          51,
                        ), // 0.2 opacity
                        width: 1,
                      ),
                    ),
                    child: ListTile(
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 4,
                      ),
                      leading: Icon(Icons.memory, color: AppTheme.primaryColor),
                      title: Text(
                        device.platformName.isNotEmpty
                            ? device.platformName
                            : 'ESP32 Device',
                        style: TextStyle(
                          color: AppTheme.textPrimaryColor,
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                      subtitle: Text(
                        'ID: ${device.remoteId.str.substring(device.remoteId.str.length - 8)}',
                        style: TextStyle(
                          color: AppTheme.textSecondaryColor,
                          fontSize: 12,
                        ),
                      ),
                      trailing: OutlinedButton(
                        onPressed: () => _connectToDevice(device),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppTheme.primaryColor,
                          side: BorderSide(color: AppTheme.primaryColor),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 0,
                          ),
                          minimumSize: const Size(80, 30),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                        child: const Text(
                          'Connect',
                          style: TextStyle(fontSize: 12),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDeviceHeader(DeviceService deviceService) {
    final connectedDevices =
        deviceService.devices.where((d) => d.lastSync != null).length;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(25),
          bottomRight: Radius.circular(25),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 opacity
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Manage Your Devices',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            'Add, remove, or edit your connected health monitoring devices.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 15),
          Row(
            children: [
              _buildStatCard(
                '${deviceService.devices.length}',
                'Total Devices',
                AppTheme.primaryColor,
                Icons.devices_other,
              ),
              const SizedBox(width: 15),
              _buildStatCard(
                '$connectedDevices',
                'Connected',
                AppTheme.secondaryColor,
                Icons.wifi,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String value,
    String label,
    Color color,
    IconData icon,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 10),
        decoration: BoxDecoration(
          color: color.withAlpha(26), // 0.1 opacity
          borderRadius: BorderRadius.circular(15),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withAlpha(51), // 0.2 opacity
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 10),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: color.withAlpha(204),
                  ), // 0.8 opacity
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceList(DeviceService deviceService) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: deviceService.devices.length,
      itemBuilder: (context, index) {
        final device = deviceService.devices[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          color: AppTheme.surfaceColor,
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withAlpha(26), // 0.1 opacity
                shape: BoxShape.circle,
              ),
              child: Icon(Icons.devices_other, color: AppTheme.primaryColor),
            ),
            title: Row(
              children: [
                Text(
                  (device.customName?.isNotEmpty ?? false)
                      ? device.customName!
                      : device.deviceType ?? 'Unknown Device',
                  style: TextStyle(
                    color: AppTheme.textPrimaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 8),
                // Connection status indicator
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color:
                        device.isConnected == true ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Connection: ${device.isConnected == true ? (device.connectionType?.toString().split('.').last.toUpperCase() ?? "BLE") : "Offline"}',
                  style: TextStyle(
                    color:
                        device.isConnected == true
                            ? Colors.green
                            : AppTheme.textSecondaryColor,
                  ),
                ),
                Text(
                  'Name: ${device.customName ?? "Unknown"}',
                  style: TextStyle(color: AppTheme.textSecondaryColor),
                ),
                if (device.bleMacAddress != null)
                  Text(
                    'BLE MAC: ${device.bleMacAddress}',
                    style: TextStyle(
                      color: AppTheme.textSecondaryColor,
                      fontSize: 12,
                    ),
                  ),
                Text(
                  'Storage: ${device.storageStatus?.toString().split('.').last ?? "Unknown"}',
                  style: TextStyle(
                    color:
                        device.storageStatus == StorageStatus.full
                            ? AppTheme.errorColor
                            : AppTheme.textSecondaryColor,
                  ),
                ),
                Text(
                  'Battery: ${device.batteryLevel ?? 0}%',
                  style: TextStyle(
                    color:
                        (device.batteryLevel ?? 0) <= 10
                            ? AppTheme.errorColor
                            : AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
            trailing: IconButton(
              icon: Icon(Icons.more_vert, color: AppTheme.primaryColor),
              onPressed: () => _showDeviceOptions(device),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.devices_other,
            size: 64,
            color: AppTheme.primaryColor.withAlpha(128), // 0.5 opacity
          ),
          const SizedBox(height: 16),
          Text(
            'No Devices Found',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: AppTheme.textPrimaryColor),
          ),
          const SizedBox(height: 8),
          Text(
            'Add a new device to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showAddDeviceDialog,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            icon: const Icon(Icons.add),
            label: const Text('Add Device'),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.surfaceColor,
            title: Text(
              'Device Management Help',
              style: TextStyle(color: AppTheme.textPrimaryColor),
            ),
            content: SingleChildScrollView(
              child: Text(
                'Here you can manage your health monitoring devices. '
                'Add new devices, monitor their battery levels, and check their connection status. '
                'Make sure your devices are within range and have sufficient battery power for optimal performance.',
                style: TextStyle(color: AppTheme.textSecondaryColor),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.primaryColor,
                ),
                child: const Text('Got it'),
              ),
            ],
          ),
    );
  }

  void _showAddDeviceDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.surfaceColor,
            title: Text(
              'Add New Device',
              style: TextStyle(color: AppTheme.textPrimaryColor),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Scan for BLE devices to connect:',
                  style: TextStyle(color: AppTheme.textSecondaryColor),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pop(context);
                        _startScan();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                      icon: const Icon(Icons.bluetooth_searching),
                      label: const Text('Start Scanning'),
                    ),
                  ],
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.primaryColor,
                ),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  void _showDeviceOptions(Device device) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.surfaceColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppTheme.textTertiaryColor,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              // Device name with edit button
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        (device.customName?.isNotEmpty ?? false)
                            ? device.customName!
                            : device.deviceType ?? 'Unknown Device',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.edit, color: AppTheme.primaryColor),
                      onPressed: () {
                        Navigator.pop(context);
                        _showRenameDeviceDialog(device);
                      },
                    ),
                  ],
                ),
              ),
              const Divider(),
              // Sync Device option
              ListTile(
                leading: Icon(Icons.sync, color: AppTheme.primaryColor),
                title: Text(
                  'Sync Device',
                  style: TextStyle(color: AppTheme.textPrimaryColor),
                ),
                subtitle: Text(
                  'Update data from this device',
                  style: TextStyle(
                    color: AppTheme.textSecondaryColor,
                    fontSize: 12,
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _syncDevice(device);
                },
              ),
              // Connect/Disconnect option
              ListTile(
                leading: Icon(
                  device.isConnected == true
                      ? Icons.bluetooth_disabled
                      : Icons.bluetooth_connected,
                  color:
                      device.isConnected == true
                          ? AppTheme.warningColor
                          : AppTheme.primaryColor,
                ),
                title: Text(
                  device.isConnected == true
                      ? 'Disconnect Device'
                      : 'Connect Device',
                  style: TextStyle(
                    color:
                        device.isConnected == true
                            ? AppTheme.warningColor
                            : AppTheme.primaryColor,
                  ),
                ),
                subtitle: Text(
                  device.isConnected == true
                      ? 'Disconnect from this device'
                      : 'Establish connection with this device',
                  style: TextStyle(
                    color: AppTheme.textSecondaryColor,
                    fontSize: 12,
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _toggleDeviceConnection(device);
                },
              ),
              // Remove Device option
              ListTile(
                leading: Icon(Icons.delete_outline, color: AppTheme.errorColor),
                title: Text(
                  'Remove Device',
                  style: TextStyle(color: AppTheme.errorColor),
                ),
                subtitle: Text(
                  'Forget this device and remove it from your list',
                  style: TextStyle(
                    color: AppTheme.textSecondaryColor,
                    fontSize: 12,
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _showRemoveDeviceConfirmation(device);
                },
              ),
              const SizedBox(height: 16),
            ],
          ),
    );
  }

  // Show dialog to rename a device
  void _showRenameDeviceDialog(Device device) {
    final TextEditingController nameController = TextEditingController(
      text:
          (device.customName?.isNotEmpty ?? false)
              ? device.customName!
              : device.deviceType ?? 'Unknown Device',
    );

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.surfaceColor,
            title: Text(
              'Rename Device',
              style: TextStyle(color: AppTheme.textPrimaryColor),
            ),
            content: TextField(
              controller: nameController,
              decoration: InputDecoration(
                labelText: 'Device Name',
                labelStyle: TextStyle(color: AppTheme.textSecondaryColor),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: AppTheme.primaryColor),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: AppTheme.textTertiaryColor),
                ),
              ),
              style: TextStyle(color: AppTheme.textPrimaryColor),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.textSecondaryColor,
                ),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _renameDevice(device, nameController.text);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Save'),
              ),
            ],
          ),
    );
  }

  // Rename a device
  void _renameDevice(Device device, String newName) {
    if (newName.isEmpty) return;

    final deviceService = Provider.of<DeviceService>(context, listen: false);
    deviceService.updateDeviceName(device.deviceId!, newName);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Device renamed to "$newName"'),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // Sync a device
  void _syncDevice(Device device) {
    // In a real app, this would communicate with the device to sync data
    final deviceService = Provider.of<DeviceService>(context, listen: false);
    deviceService.syncDevice(device.deviceId!);

    String deviceName =
        (device.customName?.isNotEmpty ?? false)
            ? device.customName!
            : device.deviceType ?? 'Unknown Device';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Device "$deviceName" synced successfully'),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // Toggle device connection
  Future<void> _toggleDeviceConnection(Device device) async {
    final deviceService = Provider.of<DeviceService>(context, listen: false);
    final bleManager = Provider.of<BleManager>(context, listen: false);

    bool currentConnectionState = device.isConnected ?? false;
    String deviceName =
        (device.customName?.isNotEmpty ?? false)
            ? device.customName!
            : device.deviceType ?? 'Unknown Device';

    if (currentConnectionState) {
      // Disconnect the device
      debugPrint('Disconnecting device: ${device.deviceId}');

      // Show disconnecting dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => const AlertDialog(
              content: Row(
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 16),
                  Text('Disconnecting device...'),
                ],
              ),
            ),
      );

      try {
        // Disconnect via BLE manager
        await bleManager.disconnectDevice(forgetDevice: false);

        // Update device service
        await deviceService.updateDeviceConnectionStatus(
          device.deviceId!,
          false,
        );

        // Hide dialog
        if (mounted) Navigator.of(context).pop();

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Device "$deviceName" disconnected successfully'),
              backgroundColor: AppTheme.warningColor,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } catch (e) {
        // Hide dialog
        if (mounted) Navigator.of(context).pop();

        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to disconnect device: $e'),
              backgroundColor: AppTheme.errorColor,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } else {
      // Try to reconnect to the device
      debugPrint('Reconnecting to device: ${device.deviceId}');

      // Show connecting dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => const AlertDialog(
              content: Row(
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 16),
                  Text('Reconnecting to device...'),
                ],
              ),
            ),
      );

      try {
        // Try scanning for reconnection
        await _startScanForReconnection(device);
      } catch (e) {
        debugPrint('Reconnection failed: $e');

        // Hide dialog
        if (mounted) Navigator.of(context).pop();

        // Show error with more helpful message
        if (mounted) {
          showDialog(
            context: context,
            builder:
                (context) => AlertDialog(
                  title: const Text('Reconnection Failed'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Could not reconnect to ${device.customName ?? device.deviceType ?? 'device'}.',
                      ),
                      const SizedBox(height: 8),
                      const Text('Please try:'),
                      const Text('• Make sure the device is powered on'),
                      const Text('• Check if the device is nearby'),
                      const Text('• Use the scan button to find the device'),
                      const SizedBox(height: 8),
                      Text(
                        'Error: $e',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('OK'),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        // Start a regular scan
                        _startScan();
                      },
                      child: const Text('Scan for Devices'),
                    ),
                  ],
                ),
          );
        }
      }
    }
  }

  // Start scan specifically for reconnection to a known device
  Future<void> _startScanForReconnection(Device device) async {
    try {
      debugPrint(
        'Starting scan for reconnection to device: ${device.deviceId}',
      );

      // Make sure Bluetooth is on
      if (FlutterBluePlus.adapterStateNow != BluetoothAdapterState.on) {
        throw Exception('Bluetooth is not enabled');
      }

      // Check if we're already scanning and stop it
      if (await FlutterBluePlus.isScanning.first) {
        await FlutterBluePlus.stopScan();
        await Future.delayed(const Duration(milliseconds: 500));
      }

      bool deviceFound = false;
      StreamSubscription<List<ScanResult>>? reconnectionSubscription;

      // Set up the scan results listener first
      reconnectionSubscription = FlutterBluePlus.scanResults.listen((
        results,
      ) async {
        if (deviceFound) return; // Prevent multiple connections

        for (ScanResult result in results) {
          // Check if this is the device we're looking for
          if (result.device.remoteId.toString() == device.deviceId) {
            debugPrint(
              'Found target device for reconnection: ${result.device.remoteId}',
            );
            deviceFound = true;

            // Cancel the subscription and stop scanning
            await reconnectionSubscription?.cancel();
            await FlutterBluePlus.stopScan();

            // Connect to the found device
            await _reconnectToFoundDevice(result.device, device);
            return;
          }
        }
      });

      // Start scanning
      await FlutterBluePlus.startScan(
        timeout: const Duration(seconds: 15), // Increased timeout
        androidScanMode: AndroidScanMode.lowLatency,
      );

      // Wait for scan to complete
      await FlutterBluePlus.isScanning.where((val) => val == false).first;

      // Clean up subscription
      await reconnectionSubscription.cancel();

      if (!deviceFound) {
        throw Exception(
          'Device not found during scan. Make sure the device is nearby and powered on.',
        );
      }
    } catch (e) {
      debugPrint('Error during reconnection scan: $e');
      rethrow;
    }
  }

  // Reconnect to a found device
  Future<void> _reconnectToFoundDevice(
    BluetoothDevice bleDevice,
    Device savedDevice,
  ) async {
    try {
      debugPrint('Attempting to reconnect to device: ${bleDevice.remoteId}');

      // Use the same connection logic as the original _connectToDevice method
      final bleManager = Provider.of<BleManager>(context, listen: false);
      final success = await bleManager.connectToDevice(bleDevice);

      if (success) {
        // Create device info for adding to list (same as original method)
        final deviceInfo = {
          'name':
              savedDevice.customName ??
              (bleDevice.platformName.isNotEmpty
                  ? bleDevice.platformName
                  : 'SmartInhealer Device'),
          'id': bleDevice.remoteId.str,
          'deviceType': 'SmartInhealer',
          'macAddress': bleDevice.remoteId.str,
          'batteryLevel': savedDevice.batteryLevel,
        };

        // Update the existing device in the list
        _addConnectedDeviceToList(deviceInfo);

        // Hide dialog
        if (mounted) Navigator.of(context).pop();

        // Show success message
        if (mounted) {
          String deviceName = deviceInfo['name'] as String;
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Device "$deviceName" reconnected successfully'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }

        debugPrint('Successfully reconnected to device: ${bleDevice.remoteId}');
      } else {
        throw Exception('Failed to establish BLE connection');
      }
    } catch (e) {
      debugPrint('Error reconnecting to device: $e');
      rethrow;
    }
  }

  // Show confirmation dialog for removing a device
  void _showRemoveDeviceConfirmation(Device device) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.surfaceColor,
            title: Text(
              'Remove Device',
              style: TextStyle(color: AppTheme.errorColor),
            ),
            content: Text(
              'Are you sure you want to remove "${(device.customName?.isNotEmpty ?? false) ? device.customName! : device.deviceType ?? 'Unknown Device'}" from your devices? This action cannot be undone.',
              style: TextStyle(color: AppTheme.textSecondaryColor),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.textSecondaryColor,
                ),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _removeDevice(device);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.errorColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Remove'),
              ),
            ],
          ),
    );
  }

  // Remove a device
  Future<void> _removeDevice(Device device) async {
    final deviceService = Provider.of<DeviceService>(context, listen: false);
    final bleManager = Provider.of<BleManager>(context, listen: false);

    String deviceName =
        (device.customName?.isNotEmpty ?? false)
            ? device.customName!
            : device.deviceType ?? 'Unknown Device';

    // Show removing dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('Removing device...'),
              ],
            ),
          ),
    );

    try {
      // If device is connected, disconnect it first
      if (device.isConnected == true) {
        debugPrint('Disconnecting device before removal: ${device.deviceId}');
        await bleManager.forceDisconnectAndForget();
      }

      // Remove device from service
      await deviceService.removeDevice(device.deviceId!);

      // Hide dialog
      if (mounted) Navigator.of(context).pop();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Device "$deviceName" removed successfully'),
            backgroundColor: AppTheme.warningColor,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      // Hide dialog
      if (mounted) Navigator.of(context).pop();

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to remove device: $e'),
            backgroundColor: AppTheme.errorColor,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}
