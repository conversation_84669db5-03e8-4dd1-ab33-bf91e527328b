import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:befine/models/measurement_data.dart';
import 'package:befine/theme/app_theme.dart';

/// Widget for displaying flow rate over time
class FlowChart extends StatelessWidget {
  /// Flow data points to display
  final List<MeasurementPoint> flowData;
  
  /// Optional maximum Y value
  final double? maxY;
  
  /// Optional minimum Y value
  final double? minY;
  
  /// Whether to use dynamic scaling based on data
  final bool useDynamicScaling;

  /// Constructor
  const FlowChart({
    super.key,
    required this.flowData,
    this.maxY, // Optional max flow rate (L/s)
    this.minY, // Optional min flow rate (L/s)
    this.useDynamicScaling = true, // Use dynamic scaling by default
  });

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1.5,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Débit (L/s)',
              style: TextStyle(
                fontSize: 18, 
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(child: LineChart(_flowChartData())),
          ],
        ),
      ),
    );
  }

  LineChartData _flowChartData() {
    // Calculate dynamic min and max Y values if needed
    double effectiveMinY;
    double effectiveMaxY;

    if (useDynamicScaling && flowData.isNotEmpty) {
      // Find min and max values in the data
      double dataMinY = flowData
          .map((p) => p.value)
          .reduce((a, b) => a < b ? a : b);
      double dataMaxY = flowData
          .map((p) => p.value)
          .reduce((a, b) => a > b ? a : b);

      // Add some padding (10% of the range)
      double range = (dataMaxY - dataMinY).abs();
      double padding = range * 0.1;

      // Ensure we have at least some range
      if (range < 0.1) {
        padding = 0.5; // Default padding if range is very small
      }

      effectiveMinY = dataMinY - padding;
      effectiveMaxY = dataMaxY + padding;

      // Ensure we always include zero in the range for flow
      if (effectiveMinY > 0) effectiveMinY = 0;
      if (effectiveMaxY < 0) effectiveMaxY = 0;
    } else {
      // Use provided values or defaults
      effectiveMinY = minY ?? -10.0;
      effectiveMaxY = maxY ?? 10.0;
    }

    // Calculate appropriate interval for Y axis
    double yRange = (effectiveMaxY - effectiveMinY).abs();
    double yInterval = 1.0; // Default interval

    if (yRange > 20) {
      yInterval = 5.0;
    } else if (yRange > 10) {
      yInterval = 2.0;
    } else if (yRange < 5) {
      yInterval = 0.5;
    }

    // Calculate appropriate interval for X axis
    double xMax = flowData.isEmpty ? 10 : flowData.last.time + 1;
    double xInterval = 1.0; // Default interval

    if (xMax > 20) {
      xInterval = 5.0;
    } else if (xMax > 10) {
      xInterval = 2.0;
    }

    return LineChartData(
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        horizontalInterval: yInterval,
        verticalInterval: xInterval,
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: xInterval,
            getTitlesWidget: (value, meta) {
              return Text(
                value.toInt().toString(),
                style: TextStyle(
                  color: AppTheme.textPrimaryColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              );
            },
          ),
          axisNameWidget: Text(
            'Temps (s)',
            style: TextStyle(
              fontSize: 14, 
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: yInterval,
            getTitlesWidget: (value, meta) {
              // Format with 1 decimal place if interval is less than 1
              String label =
                  yInterval < 1
                      ? value.toStringAsFixed(1)
                      : value.toInt().toString();
              return Text(
                label,
                style: TextStyle(
                  color: AppTheme.textPrimaryColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              );
            },
            reservedSize: 40,
          ),
          axisNameWidget: Text(
            'Débit (L/s)',
            style: TextStyle(
              fontSize: 14, 
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: Colors.grey.shade300),
      ),
      minX: flowData.isEmpty ? 0 : flowData.first.time,
      maxX: flowData.isEmpty ? 10 : flowData.last.time + 1,
      minY: effectiveMinY,
      maxY: effectiveMaxY,
      lineBarsData: [
        LineChartBarData(
          spots:
              flowData.map((point) {
                return FlSpot(point.time, point.value);
              }).toList(),
          isCurved: true,
          color: AppTheme.primaryColor,
          barWidth: 3,
          isStrokeCapRound: true,
          dotData: const FlDotData(show: false),
          belowBarData: BarAreaData(
            show: true,
            color: AppTheme.primaryColor.withAlpha(50),
          ),
        ),
      ],
    );
  }
}
