import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

import '../models/device_model.dart';
import 'device_service.dart';

enum BleConnectionState { disconnected, scanning, connecting, connected, error }

class BleManager extends ChangeNotifier {
  // Connection state
  BleConnectionState _connectionState = BleConnectionState.disconnected;
  BleConnectionState get connectionState => _connectionState;

  // Connected device
  BluetoothDevice? _connectedDevice;
  BluetoothDevice? get connectedDevice => _connectedDevice;

  // Discovered devices
  List<BluetoothDevice> _discoveredDevices = [];
  List<BluetoothDevice> get discoveredDevices => _discoveredDevices;

  // Auto-connection properties
  DeviceService? _deviceService;
  Timer? _autoConnectTimer;
  bool _isAutoConnecting = false;
  bool get isAutoConnecting => _isAutoConnecting;

  // Stream subscriptions
  StreamSubscription<BluetoothAdapterState>? _adapterStateSubscription;
  StreamSubscription<List<ScanResult>>? _scanResultsSubscription;
  StreamSubscription<BluetoothConnectionState>? _connectionStateSubscription;

  // Constructor
  BleManager() {
    // Check if platform is supported
    if (Platform.isAndroid || Platform.isIOS) {
      // Set log level for debugging
      FlutterBluePlus.setLogLevel(LogLevel.verbose, color: true);

      // Initialize BLE
      _initBle();

      // Listen to adapter state changes
      _adapterStateSubscription = FlutterBluePlus.adapterState.listen((state) {
        debugPrint('Bluetooth adapter state: $state');
        if (state == BluetoothAdapterState.off) {
          // Bluetooth turned off, update state
          _connectionState = BleConnectionState.disconnected;
          _stopAutoConnectTimer();
          notifyListeners();
        } else if (state == BluetoothAdapterState.on) {
          // Bluetooth turned on, start auto-connect if enabled
          _startAutoConnectTimer();
        }
      });
    } else {
      // Platform not supported
      debugPrint('BLE not supported on this platform');
      _connectionState = BleConnectionState.error;
      notifyListeners();
    }
  }

  /// Set the device service for auto-connection functionality
  void setDeviceService(DeviceService deviceService) {
    _deviceService = deviceService;
    // Start auto-connect timer if Bluetooth is on and auto-connect is enabled
    if (FlutterBluePlus.adapterStateNow == BluetoothAdapterState.on) {
      _startAutoConnectTimer();
    }
  }

  // Initialize BLE
  Future<void> _initBle() async {
    // Skip if platform is not supported
    if (!(Platform.isAndroid || Platform.isIOS)) {
      debugPrint('BLE not supported on this platform');
      _connectionState = BleConnectionState.error;
      notifyListeners();
      return;
    }

    try {
      // Check if Bluetooth is supported
      if (await FlutterBluePlus.isSupported == false) {
        throw Exception("Bluetooth not supported on this device");
      }

      // Turn on Bluetooth if needed (Android only)
      if (Platform.isAndroid) {
        if (FlutterBluePlus.adapterStateNow == BluetoothAdapterState.off) {
          try {
            await FlutterBluePlus.turnOn();
          } catch (e) {
            debugPrint('Failed to turn on Bluetooth: $e');
          }
        }
      }
    } catch (e) {
      debugPrint('Bluetooth initialization error: $e');
      _connectionState = BleConnectionState.error;
      notifyListeners();
    }
  }

  // Start scanning for devices
  Future<void> startScan({Duration? timeout}) async {
    // Skip if platform is not supported
    if (!(Platform.isAndroid || Platform.isIOS)) {
      debugPrint('BLE not supported on this platform');
      return;
    }

    if (_connectionState == BleConnectionState.scanning) {
      return;
    }

    try {
      // Clear previous results
      _discoveredDevices = [];

      // Wait for Bluetooth to be ready
      if (FlutterBluePlus.adapterStateNow != BluetoothAdapterState.on) {
        await FlutterBluePlus.adapterState
            .where((val) => val == BluetoothAdapterState.on)
            .first
            .timeout(
              const Duration(seconds: 5),
              onTimeout: () {
                throw Exception("Bluetooth not turned on in time");
              },
            );
      }

      _connectionState = BleConnectionState.scanning;
      notifyListeners();

      // Cancel any existing subscription
      await _scanResultsSubscription?.cancel();

      // Listen for scan results
      _scanResultsSubscription = FlutterBluePlus.scanResults.listen(
        (results) {
          for (ScanResult result in results) {
            if (!_discoveredDevices.contains(result.device)) {
              _discoveredDevices.add(result.device);
              notifyListeners();
            }
          }
        },
        onError: (e) {
          debugPrint('Error during scan: $e');
          _connectionState = BleConnectionState.error;
          notifyListeners();
        },
      );

      // Start scanning
      await FlutterBluePlus.startScan(
        timeout: timeout ?? const Duration(seconds: 10),
        androidScanMode: AndroidScanMode.lowLatency,
      );

      // Wait for scan to complete
      await FlutterBluePlus.isScanning.where((val) => val == false).first;

      // Update state
      if (_connectionState == BleConnectionState.scanning) {
        _connectionState = BleConnectionState.disconnected;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error scanning for devices: $e');
      _connectionState = BleConnectionState.error;
      notifyListeners();
    } finally {
      // Make sure scanning is stopped
      if (FlutterBluePlus.isScanningNow) {
        await FlutterBluePlus.stopScan();
      }
    }
  }

  // Stop scanning
  Future<void> stopScan() async {
    if (FlutterBluePlus.isScanningNow) {
      await FlutterBluePlus.stopScan();
    }

    if (_connectionState == BleConnectionState.scanning) {
      _connectionState = BleConnectionState.disconnected;
      notifyListeners();
    }
  }

  // Connect to a device
  Future<bool> connectToDevice(BluetoothDevice device) async {
    debugPrint('=== BLE Manager: Starting connection process ===');

    // Skip if platform is not supported
    if (!(Platform.isAndroid || Platform.isIOS)) {
      debugPrint('BLE not supported on this platform');
      return false;
    }

    debugPrint('Platform check passed');

    // If already connected to this device, return success
    if (_connectedDevice != null &&
        _connectedDevice!.remoteId == device.remoteId &&
        _connectionState == BleConnectionState.connected) {
      debugPrint('Already connected to this device');
      return true;
    }

    // If connected to a different device, disconnect first
    if (_connectedDevice != null) {
      debugPrint('Disconnecting from previous device first');
      await disconnectDevice(forgetDevice: true);
      // Wait a moment for cleanup
      await Future.delayed(const Duration(milliseconds: 500));
    }

    try {
      debugPrint('Setting connection state to connecting');
      _connectionState = BleConnectionState.connecting;
      notifyListeners();

      // Cancel any existing subscription
      await _connectionStateSubscription?.cancel();
      debugPrint('Cancelled existing subscriptions');

      // Set up connection state listener
      _connectionStateSubscription = device.connectionState.listen((state) {
        debugPrint('Device connection state changed: $state');
        if (state == BluetoothConnectionState.disconnected) {
          // Handle unexpected disconnection
          if (_connectionState == BleConnectionState.connected) {
            debugPrint('Device disconnected unexpectedly');
            _handleDisconnection();
          }
        } else if (state == BluetoothConnectionState.connected) {
          debugPrint('Device connected successfully via listener');
          if (_connectionState == BleConnectionState.connecting) {
            _connectionState = BleConnectionState.connected;
            notifyListeners();
          }
        }
      });

      debugPrint('Connection state listener set up, attempting to connect...');

      // Connect to the device with a reasonable timeout
      await device.connect(
        autoConnect: false, // Disable auto-connect for initial connection
        timeout: const Duration(seconds: 20),
      );

      debugPrint('device.connect() completed, waiting for stabilization...');

      // Wait for connection to be established
      await Future.delayed(const Duration(milliseconds: 1000));

      debugPrint('Checking connection status...');
      debugPrint('device.isConnected: ${device.isConnected}');
      debugPrint('Current connection state: $_connectionState');

      // Verify connection
      if (device.isConnected) {
        _connectedDevice = device;
        _connectionState = BleConnectionState.connected;
        _stopAutoConnectTimer(); // Stop auto-connect timer when manually connected

        // Update device service about successful connection
        if (_deviceService != null) {
          _deviceService!.updateDeviceConnectionStatus(
            device.remoteId.toString(),
            true,
          );
        }

        notifyListeners();

        debugPrint('Successfully connected to device: ${device.remoteId}');
        return true;
      } else {
        debugPrint('Connection failed - device.isConnected returned false');
        _connectionState = BleConnectionState.error;
        notifyListeners();
        return false;
      }
    } catch (e) {
      debugPrint('Exception during connection: $e');
      debugPrint('Exception type: ${e.runtimeType}');
      _connectionState = BleConnectionState.error;
      notifyListeners();

      // Clean up on error
      await _connectionStateSubscription?.cancel();
      _connectionStateSubscription = null;

      return false;
    }
  }

  // Handle unexpected disconnection
  void _handleDisconnection() {
    final disconnectedDeviceId = _connectedDevice?.remoteId.toString();

    _connectionState = BleConnectionState.disconnected;
    notifyListeners();

    // Update device service about disconnection
    if (_deviceService != null && disconnectedDeviceId != null) {
      _deviceService!.updateDeviceConnectionStatus(disconnectedDeviceId, false);
    }

    // Start auto-reconnect timer if enabled
    _startAutoConnectTimer();
  }

  /// Start auto-connect timer to periodically check for last connected device
  void _startAutoConnectTimer() {
    // Don't start if already running, no device service, or auto-connect disabled
    if (_autoConnectTimer != null ||
        _deviceService == null ||
        !_deviceService!.autoConnectEnabled ||
        _connectionState == BleConnectionState.connected) {
      return;
    }

    debugPrint('Starting auto-connect timer');
    _autoConnectTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _attemptAutoConnect();
    });
  }

  /// Stop auto-connect timer
  void _stopAutoConnectTimer() {
    if (_autoConnectTimer != null) {
      debugPrint('Stopping auto-connect timer');
      _autoConnectTimer!.cancel();
      _autoConnectTimer = null;
    }
  }

  /// Attempt to auto-connect to the last connected device
  Future<void> _attemptAutoConnect() async {
    // Skip if already connected, connecting, or auto-connect disabled
    if (_connectionState == BleConnectionState.connected ||
        _connectionState == BleConnectionState.connecting ||
        _deviceService == null ||
        !_deviceService!.autoConnectEnabled ||
        _isAutoConnecting) {
      return;
    }

    final lastDevice = _deviceService!.getLastConnectedDevice();
    if (lastDevice == null || lastDevice.deviceId == null) {
      debugPrint('No last connected device found for auto-connect');
      return;
    }

    debugPrint('Attempting auto-connect to device: ${lastDevice.deviceId}');
    _isAutoConnecting = true;
    notifyListeners();

    try {
      // Try to find the device by device ID (which is the BLE MAC address)
      final success = await reconnectToDeviceById(lastDevice.deviceId!);
      if (success) {
        debugPrint('Auto-connect successful to device: ${lastDevice.deviceId}');
        _stopAutoConnectTimer(); // Stop timer when connected
      } else {
        debugPrint(
          'Auto-connect failed for device: ${lastDevice.deviceId}, will retry later',
        );
      }
    } catch (e) {
      debugPrint('Auto-connect error for device ${lastDevice.deviceId}: $e');
    } finally {
      _isAutoConnecting = false;
      notifyListeners();
    }
  }

  // Disconnect from device
  Future<void> disconnectDevice({bool forgetDevice = true}) async {
    try {
      debugPrint('Disconnecting device (forget: $forgetDevice)');

      final deviceIdToDisconnect = _connectedDevice?.remoteId.toString();

      // Cancel connection state subscription
      await _connectionStateSubscription?.cancel();
      _connectionStateSubscription = null;

      // Disconnect device if connected
      if (_connectedDevice != null) {
        try {
          await _connectedDevice!.disconnect();
          debugPrint('Device disconnected successfully');
        } catch (e) {
          debugPrint('Error during disconnect: $e');
          // Continue with cleanup even if disconnect fails
        }
      }

      // Update device service about disconnection
      if (_deviceService != null && deviceIdToDisconnect != null) {
        _deviceService!.updateDeviceConnectionStatus(
          deviceIdToDisconnect,
          false,
        );
      }

      // Clear last connected device if forgetting
      if (forgetDevice && _deviceService != null) {
        _deviceService!.clearLastConnectedDevice();
      }

      // Clear the reference if we want to forget the device
      if (forgetDevice) {
        _connectedDevice = null;
        debugPrint('Device reference cleared');
      }

      // Update state
      _connectionState = BleConnectionState.disconnected;
      notifyListeners();

      debugPrint('Disconnect operation completed');
    } catch (e) {
      debugPrint('Error during disconnection cleanup: $e');
      // Ensure we still update the state
      _connectionState = BleConnectionState.disconnected;
      notifyListeners();
    }
  }

  // Force disconnect and forget device
  Future<void> forceDisconnectAndForget() async {
    debugPrint('Force disconnecting and forgetting device');
    await disconnectDevice(forgetDevice: true);

    // Additional cleanup
    _connectedDevice = null;
    _connectionState = BleConnectionState.disconnected;
    notifyListeners();
  }

  // Get a BluetoothDevice by its ID (for reconnection)
  Future<BluetoothDevice?> findDeviceById(String deviceId) async {
    try {
      // Check if we already have a reference to this device
      if (_connectedDevice != null &&
          _connectedDevice!.remoteId.toString() == deviceId) {
        return _connectedDevice;
      }

      // Get list of connected devices from the system
      List<BluetoothDevice> connectedDevices = FlutterBluePlus.connectedDevices;
      for (BluetoothDevice device in connectedDevices) {
        if (device.remoteId.toString() == deviceId) {
          return device;
        }
      }

      // If not found in connected devices, we need to scan for it
      debugPrint('Device not found in connected devices, scanning required');
      return null;
    } catch (e) {
      debugPrint('Error finding device by ID: $e');
      return null;
    }
  }

  // Reconnect to a device by ID
  Future<bool> reconnectToDeviceById(String deviceId) async {
    try {
      debugPrint('Attempting to reconnect to device: $deviceId');

      // First try to find the device in connected devices
      BluetoothDevice? device = await findDeviceById(deviceId);

      if (device != null) {
        // Device found, try to connect
        return await connectToDevice(device);
      } else {
        debugPrint('Device not found, scanning required');
        return false;
      }
    } catch (e) {
      debugPrint('Error during reconnection: $e');
      return false;
    }
  }

  // Get device battery level if available
  Future<int?> getDeviceBatteryLevel() async {
    if (_connectedDevice == null ||
        _connectionState != BleConnectionState.connected) {
      return null;
    }

    try {
      // Discover services only once
      List<BluetoothService> services =
          await _connectedDevice!.discoverServices();

      // Look for our SmartInhealer service first
      for (BluetoothService service in services) {
        if (service.uuid.toString().toLowerCase() ==
            BleUuids.serviceUuid.toLowerCase()) {
          debugPrint('Found SmartInhealer service: ${service.uuid}');

          // Look for status characteristic
          for (BluetoothCharacteristic c in service.characteristics) {
            if (c.uuid.toString().toLowerCase() ==
                BleUuids.statusCharacteristicUuid.toLowerCase()) {
              try {
                final value = await c.read();
                if (value.isNotEmpty) {
                  // Battery level is in the first byte
                  debugPrint('Battery level: ${value[0]}%');
                  return value[0];
                }
              } catch (e) {
                debugPrint('Error reading status characteristic: $e');
              }
            }
          }
        }
      }

      debugPrint('SmartInhealer service or status characteristic not found');
      return null;
    } catch (e) {
      debugPrint('Error getting battery level: $e');
      return null;
    }
  }

  // Get the SmartInhealer service and characteristics
  Future<Map<String, BluetoothCharacteristic>?>
  getSmartInhalerCharacteristics() async {
    if (_connectedDevice == null ||
        _connectionState != BleConnectionState.connected) {
      return null;
    }

    try {
      // Discover services
      List<BluetoothService> services =
          await _connectedDevice!.discoverServices();

      // Find our SmartInhealer service
      for (BluetoothService service in services) {
        if (service.uuid.toString().toLowerCase() ==
            BleUuids.serviceUuid.toLowerCase()) {
          debugPrint('Found SmartInhealer service: ${service.uuid}');

          Map<String, BluetoothCharacteristic> characteristics = {};

          // Look for our characteristics
          for (BluetoothCharacteristic c in service.characteristics) {
            if (c.uuid.toString().toLowerCase() ==
                BleUuids.dataCharacteristicUuid.toLowerCase()) {
              characteristics['data'] = c;
              debugPrint('Found data characteristic: ${c.uuid}');
            } else if (c.uuid.toString().toLowerCase() ==
                BleUuids.statusCharacteristicUuid.toLowerCase()) {
              characteristics['status'] = c;
              debugPrint('Found status characteristic: ${c.uuid}');
            }
          }

          if (characteristics.isNotEmpty) {
            return characteristics;
          }
        }
      }

      debugPrint('SmartInhealer service not found');
      return null;
    } catch (e) {
      debugPrint('Error discovering SmartInhealer characteristics: $e');
      return null;
    }
  }

  @override
  void dispose() {
    // Cancel all subscriptions
    _scanResultsSubscription?.cancel();
    _connectionStateSubscription?.cancel();
    _adapterStateSubscription?.cancel();

    // Stop auto-connect timer
    _stopAutoConnectTimer();

    // Disconnect device if connected
    if (_connectedDevice != null) {
      try {
        _connectedDevice!.disconnect();
      } catch (e) {
        debugPrint('Error disconnecting device during dispose: $e');
      }
    }

    debugPrint('BleManager disposed');
    super.dispose();
  }
}
