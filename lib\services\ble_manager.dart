import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';

import '../models/device_model.dart';

enum BleConnectionState { disconnected, scanning, connecting, connected, error }

class BleManager extends ChangeNotifier {
  // Connection state
  BleConnectionState _connectionState = BleConnectionState.disconnected;
  BleConnectionState get connectionState => _connectionState;

  // Connected device
  BluetoothDevice? _connectedDevice;
  BluetoothDevice? get connectedDevice => _connectedDevice;

  // Discovered devices
  List<BluetoothDevice> _discoveredDevices = [];
  List<BluetoothDevice> get discoveredDevices => _discoveredDevices;

  // Stream subscriptions
  StreamSubscription<BluetoothAdapterState>? _adapterStateSubscription;
  StreamSubscription<List<ScanResult>>? _scanResultsSubscription;
  StreamSubscription<BluetoothConnectionState>? _connectionStateSubscription;

  // Constructor
  BleManager() {
    // Check if platform is supported
    if (Platform.isAndroid || Platform.isIOS) {
      // Set log level for debugging
      FlutterBluePlus.setLogLevel(LogLevel.verbose, color: true);

      // Initialize BLE
      _initBle();

      // Listen to adapter state changes
      _adapterStateSubscription = FlutterBluePlus.adapterState.listen((state) {
        debugPrint('Bluetooth adapter state: $state');
        if (state == BluetoothAdapterState.off) {
          // Bluetooth turned off, update state
          _connectionState = BleConnectionState.disconnected;
          notifyListeners();
        }
      });
    } else {
      // Platform not supported
      debugPrint('BLE not supported on this platform');
      _connectionState = BleConnectionState.error;
      notifyListeners();
    }
  }

  // Initialize BLE
  Future<void> _initBle() async {
    // Skip if platform is not supported
    if (!(Platform.isAndroid || Platform.isIOS)) {
      debugPrint('BLE not supported on this platform');
      _connectionState = BleConnectionState.error;
      notifyListeners();
      return;
    }

    try {
      // Check if Bluetooth is supported
      if (await FlutterBluePlus.isSupported == false) {
        throw Exception("Bluetooth not supported on this device");
      }

      // Turn on Bluetooth if needed (Android only)
      if (Platform.isAndroid) {
        if (FlutterBluePlus.adapterStateNow == BluetoothAdapterState.off) {
          try {
            await FlutterBluePlus.turnOn();
          } catch (e) {
            debugPrint('Failed to turn on Bluetooth: $e');
          }
        }
      }
    } catch (e) {
      debugPrint('Bluetooth initialization error: $e');
      _connectionState = BleConnectionState.error;
      notifyListeners();
    }
  }

  // Start scanning for devices
  Future<void> startScan({Duration? timeout}) async {
    // Skip if platform is not supported
    if (!(Platform.isAndroid || Platform.isIOS)) {
      debugPrint('BLE not supported on this platform');
      return;
    }

    if (_connectionState == BleConnectionState.scanning) {
      return;
    }

    try {
      // Clear previous results
      _discoveredDevices = [];

      // Wait for Bluetooth to be ready
      if (FlutterBluePlus.adapterStateNow != BluetoothAdapterState.on) {
        await FlutterBluePlus.adapterState
            .where((val) => val == BluetoothAdapterState.on)
            .first
            .timeout(
              const Duration(seconds: 5),
              onTimeout: () {
                throw Exception("Bluetooth not turned on in time");
              },
            );
      }

      _connectionState = BleConnectionState.scanning;
      notifyListeners();

      // Cancel any existing subscription
      await _scanResultsSubscription?.cancel();

      // Listen for scan results
      _scanResultsSubscription = FlutterBluePlus.scanResults.listen(
        (results) {
          for (ScanResult result in results) {
            if (!_discoveredDevices.contains(result.device)) {
              _discoveredDevices.add(result.device);
              notifyListeners();
            }
          }
        },
        onError: (e) {
          debugPrint('Error during scan: $e');
          _connectionState = BleConnectionState.error;
          notifyListeners();
        },
      );

      // Start scanning
      await FlutterBluePlus.startScan(
        timeout: timeout ?? const Duration(seconds: 10),
        androidScanMode: AndroidScanMode.lowLatency,
      );

      // Wait for scan to complete
      await FlutterBluePlus.isScanning.where((val) => val == false).first;

      // Update state
      if (_connectionState == BleConnectionState.scanning) {
        _connectionState = BleConnectionState.disconnected;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error scanning for devices: $e');
      _connectionState = BleConnectionState.error;
      notifyListeners();
    } finally {
      // Make sure scanning is stopped
      if (FlutterBluePlus.isScanningNow) {
        await FlutterBluePlus.stopScan();
      }
    }
  }

  // Stop scanning
  Future<void> stopScan() async {
    if (FlutterBluePlus.isScanningNow) {
      await FlutterBluePlus.stopScan();
    }

    if (_connectionState == BleConnectionState.scanning) {
      _connectionState = BleConnectionState.disconnected;
      notifyListeners();
    }
  }

  // Connect to a device
  Future<bool> connectToDevice(BluetoothDevice device) async {
    // Skip if platform is not supported
    if (!(Platform.isAndroid || Platform.isIOS)) {
      debugPrint('BLE not supported on this platform');
      return false;
    }

    // If already connected to this device, return success
    if (_connectedDevice != null &&
        _connectedDevice!.remoteId == device.remoteId &&
        _connectionState == BleConnectionState.connected) {
      debugPrint('Already connected to this device');
      return true;
    }

    // If connected to a different device, disconnect first
    if (_connectedDevice != null &&
        _connectedDevice!.remoteId != device.remoteId) {
      await disconnectDevice();
    }

    try {
      _connectionState = BleConnectionState.connecting;
      notifyListeners();

      // Set up connection state listener
      await _connectionStateSubscription?.cancel();
      _connectionStateSubscription = device.connectionState.listen((state) {
        debugPrint('Device connection state changed: $state');
        if (state == BluetoothConnectionState.disconnected) {
          // Handle unexpected disconnection
          if (_connectionState == BleConnectionState.connected) {
            debugPrint('Device disconnected unexpectedly');
            _handleDisconnection();

            // Try to reconnect automatically
            _tryReconnect(device);
          }
        }
      });

      // Connect to the device
      await device.connect(
        autoConnect: true, // Enable auto-reconnect
        timeout: const Duration(seconds: 15),
      );

      _connectedDevice = device;
      _connectionState = BleConnectionState.connected;
      notifyListeners();

      // Save the device ID to persistent storage for future reconnection
      _saveConnectedDeviceId(device.remoteId.str);

      return true;
    } catch (e) {
      debugPrint('Error connecting to device: $e');
      _connectionState = BleConnectionState.error;
      notifyListeners();
      return false;
    }
  }

  // Save connected device ID for future reconnection
  void _saveConnectedDeviceId(String deviceId) {
    // In a real implementation, you would save this to SharedPreferences
    // For now, we'll just keep it in memory
    debugPrint('Saving connected device ID: $deviceId');
  }

  // Try to reconnect to the device
  Future<void> _tryReconnect(BluetoothDevice device) async {
    debugPrint('Attempting to reconnect to device: ${device.remoteId}');

    // Wait a moment before trying to reconnect
    await Future.delayed(const Duration(seconds: 2));

    // Only try to reconnect if we're still in the disconnected state
    if (_connectionState == BleConnectionState.disconnected) {
      try {
        _connectionState = BleConnectionState.connecting;
        notifyListeners();

        // Try to connect again
        await device.connect(
          autoConnect: true,
          timeout: const Duration(seconds: 10),
        );

        _connectedDevice = device;
        _connectionState = BleConnectionState.connected;
        notifyListeners();

        debugPrint('Successfully reconnected to device: ${device.remoteId}');
      } catch (e) {
        debugPrint('Failed to reconnect to device: $e');
        _connectionState = BleConnectionState.disconnected;
        notifyListeners();

        // Schedule another reconnection attempt
        Future.delayed(const Duration(seconds: 5), () => _tryReconnect(device));
      }
    }
  }

  // Handle unexpected disconnection
  void _handleDisconnection() {
    _connectionState = BleConnectionState.disconnected;
    _connectedDevice = null;
    notifyListeners();
  }

  // Disconnect from device
  Future<void> disconnectDevice({bool forgetDevice = true}) async {
    try {
      // Cancel connection state subscription
      await _connectionStateSubscription?.cancel();
      _connectionStateSubscription = null;

      // Disconnect device if connected
      if (_connectedDevice != null) {
        try {
          await _connectedDevice!.disconnect();
        } catch (e) {
          debugPrint('Error during disconnect: $e');
          // Continue with cleanup even if disconnect fails
        }
      }

      // Only clear the reference if we want to forget the device
      if (forgetDevice) {
        _connectedDevice = null;
      }

      // Update state
      _connectionState = BleConnectionState.disconnected;
      notifyListeners();
    } catch (e) {
      debugPrint('Error during disconnection cleanup: $e');
      // Ensure we still update the state
      _connectionState = BleConnectionState.disconnected;
      notifyListeners();
    }
  }

  // Get device battery level if available
  Future<int?> getDeviceBatteryLevel() async {
    if (_connectedDevice == null ||
        _connectionState != BleConnectionState.connected) {
      return null;
    }

    try {
      // Discover services
      List<BluetoothService> services =
          await _connectedDevice!.discoverServices();

      // First try to find our custom SmartInhealer service
      for (BluetoothService service in services) {
        if (service.uuid.toString().toLowerCase().contains(
          BleUuids.serviceUuid.toLowerCase().substring(0, 8),
        )) {
          debugPrint('Found SmartInhealer service: ${service.uuid}');

          // Look for status characteristic to get battery level
          for (BluetoothCharacteristic c in service.characteristics) {
            if (c.uuid.toString().toLowerCase().contains(
              BleUuids.statusCharacteristicUuid.toLowerCase().substring(0, 8),
            )) {
              try {
                final value = await c.read();
                if (value.isNotEmpty && value.length >= 2) {
                  // Assuming battery level is in the first byte
                  debugPrint(
                    'Battery level from status characteristic: ${value[0]}',
                  );
                  return value[0];
                }
              } catch (e) {
                debugPrint('Error reading status characteristic: $e');
              }
            }
          }
        }
      }

      // Fallback to standard battery service if our service is not found
      for (BluetoothService service in services) {
        if (service.uuid == Guid('0000180f-0000-1000-8000-00805f9b34fb')) {
          // Battery service UUID
          for (BluetoothCharacteristic c in service.characteristics) {
            if (c.uuid == Guid('00002a19-0000-1000-8000-00805f9b34fb')) {
              // Battery level characteristic UUID
              final value = await c.read();
              return value.isNotEmpty ? value[0] : null;
            }
          }
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error getting battery level: $e');
      return null;
    }
  }

  // Read data from the device's data characteristic
  Future<List<int>?> readDeviceData() async {
    if (_connectedDevice == null ||
        _connectionState != BleConnectionState.connected) {
      return null;
    }

    try {
      // Discover services
      List<BluetoothService> services =
          await _connectedDevice!.discoverServices();

      // Find our SmartInhealer service
      for (BluetoothService service in services) {
        if (service.uuid.toString().toLowerCase().contains(
          BleUuids.serviceUuid.toLowerCase().substring(0, 8),
        )) {
          debugPrint(
            'Found SmartInhealer service for data reading: ${service.uuid}',
          );

          // Look for data characteristic
          for (BluetoothCharacteristic c in service.characteristics) {
            if (c.uuid.toString().toLowerCase().contains(
              BleUuids.dataCharacteristicUuid.toLowerCase().substring(0, 8),
            )) {
              try {
                final value = await c.read();
                debugPrint('Read data from characteristic: $value');
                return value;
              } catch (e) {
                debugPrint('Error reading data characteristic: $e');
                return null;
              }
            }
          }
        }
      }

      debugPrint('SmartInhealer data characteristic not found');
      return null;
    } catch (e) {
      debugPrint('Error reading device data: $e');
      return null;
    }
  }

  // Write data to the device's data characteristic
  Future<bool> writeDeviceData(List<int> data) async {
    if (_connectedDevice == null ||
        _connectionState != BleConnectionState.connected) {
      return false;
    }

    try {
      // Discover services
      List<BluetoothService> services =
          await _connectedDevice!.discoverServices();

      // Find our SmartInhealer service
      for (BluetoothService service in services) {
        if (service.uuid.toString().toLowerCase().contains(
          BleUuids.serviceUuid.toLowerCase().substring(0, 8),
        )) {
          debugPrint(
            'Found SmartInhealer service for data writing: ${service.uuid}',
          );

          // Look for data characteristic
          for (BluetoothCharacteristic c in service.characteristics) {
            if (c.uuid.toString().toLowerCase().contains(
              BleUuids.dataCharacteristicUuid.toLowerCase().substring(0, 8),
            )) {
              try {
                await c.write(data);
                debugPrint('Successfully wrote data to characteristic');
                return true;
              } catch (e) {
                debugPrint('Error writing to data characteristic: $e');
                return false;
              }
            }
          }
        }
      }

      debugPrint('SmartInhealer data characteristic not found');
      return false;
    } catch (e) {
      debugPrint('Error writing device data: $e');
      return false;
    }
  }

  @override
  void dispose() {
    // Cancel all subscriptions
    _scanResultsSubscription?.cancel();
    _connectionStateSubscription?.cancel();
    _adapterStateSubscription?.cancel();

    // Disconnect device if connected
    if (_connectedDevice != null) {
      try {
        _connectedDevice!.disconnect();
      } catch (e) {
        debugPrint('Error disconnecting device during dispose: $e');
      }
    }

    debugPrint('BleManager disposed');
    super.dispose();
  }
}
