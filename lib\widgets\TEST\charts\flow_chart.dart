import 'dart:math';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:befine/models/measurement_data.dart';
import 'package:befine/theme/app_theme.dart';

/// Widget for displaying flow rate over time
class FlowChart extends StatelessWidget {
  /// Flow data points to display
  final List<MeasurementPoint> flowData;

  /// Optional maximum Y value
  final double? maxY;

  /// Optional minimum Y value
  final double? minY;

  /// Whether to use dynamic scaling based on data
  final bool useDynamicScaling;

  /// Constructor
  const FlowChart({
    super.key,
    required this.flowData,
    this.maxY,
    this.minY,
    this.useDynamicScaling = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.timeline,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Débit (L/s)',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 250,
            child: LineChart(_flowChartData()),
          ),
        ],
      ),
    );
  }

  LineChartData _flowChartData() {
    // Calculate dynamic min and max Y values if needed
    double effectiveMinY;
    double effectiveMaxY;

    if (useDynamicScaling && flowData.isNotEmpty) {
      // Find min and max values in the data
      double dataMinY = flowData.map((point) => point.value).reduce((a, b) => a < b ? a : b);
      double dataMaxY = flowData.map((point) => point.value).reduce((a, b) => a > b ? a : b);

      // Add some padding (15% of the range)
      double range = dataMaxY - dataMinY;
      double padding = range * 0.15;

      effectiveMinY = minY ?? (dataMinY - padding);
      effectiveMaxY = maxY ?? (dataMaxY + padding);

      // Ensure we have a minimum range
      if (range < 0.1) {
        effectiveMinY = (dataMinY + dataMaxY) / 2 - 0.1;
        effectiveMaxY = (dataMinY + dataMaxY) / 2 + 0.1;
      }
    } else {
      // Use provided values or defaults
      effectiveMinY = minY ?? -6.0; // Default min flow (L/min)
      effectiveMaxY = maxY ?? 2.0;  // Default max flow (L/min)
    }

    // Calculate clean intervals
    double yRange = effectiveMaxY - effectiveMinY;
    double yInterval = _calculateCleanInterval(yRange / 4);
    
    double xRange = flowData.isNotEmpty ? 
      (flowData.last.time - flowData.first.time) : 10;
    double xInterval = _calculateCleanInterval(xRange / 5);

    return LineChartData(
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        drawHorizontalLine: true,
        horizontalInterval: yInterval,
        verticalInterval: xInterval,
        getDrawingHorizontalLine: (value) {
          return FlLine(
            color: Colors.grey.shade200,
            strokeWidth: 0.8,
          );
        },
        getDrawingVerticalLine: (value) {
          return FlLine(
            color: Colors.grey.shade200,
            strokeWidth: 0.8,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 35,
            interval: xInterval,
            getTitlesWidget: (value, meta) {
              return SideTitleWidget(
                axisSide: meta.axisSide,
                child: Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    value.toStringAsFixed(1),
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                      fontSize: 11,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: yInterval,
            reservedSize: 55,
            getTitlesWidget: (value, meta) {
              return SideTitleWidget(
                axisSide: meta.axisSide,
                child: Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: Text(
                    value.toStringAsFixed(1),
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                      fontSize: 11,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: Colors.grey.shade300, width: 1),
      ),
      minX: flowData.isEmpty ? 0 : flowData.first.time,
      maxX: flowData.isEmpty ? 10 : flowData.last.time + 0.5,
      minY: effectiveMinY,
      maxY: effectiveMaxY,
      lineBarsData: [
        LineChartBarData(
          spots: flowData.map((point) {
            return FlSpot(point.time, point.value);
          }).toList(),
          isCurved: true,
          color: AppTheme.primaryColor,
          barWidth: 2.5,
          isStrokeCapRound: true,
          dotData: const FlDotData(show: false),
          belowBarData: BarAreaData(
            show: true,
            color: AppTheme.primaryColor.withAlpha(30),
          ),
        ),
      ],
    );
  }

  // Helper method to calculate clean intervals
  double _calculateCleanInterval(double rawInterval) {
    if (rawInterval <= 0) return 1.0;
    
    double magnitude = pow(10, (log(rawInterval) / ln10).floor()).toDouble();
    double normalized = rawInterval / magnitude;
    
    if (normalized <= 1) return magnitude;
    if (normalized <= 2) return 2 * magnitude;
    if (normalized <= 5) return 5 * magnitude;
    return 10 * magnitude;
  }
}
