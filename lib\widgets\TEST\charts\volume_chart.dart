import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:befine/models/measurement_data.dart';
import 'package:befine/theme/app_theme.dart';

class VolumeChart extends StatelessWidget {
  final List<MeasurementPoint> volumeData;
  final double? maxY;
  final double? minY;
  final bool useDynamicScaling;

  const VolumeChart({
    super.key,
    required this.volumeData,
    this.maxY,
    this.minY,
    this.useDynamicScaling = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.show_chart,
                color: Colors.green,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Volume (L)',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 300,
            child: LineChart(_volumeChartData()),
          ),
        ],
      ),
    );
  }

  LineChartData _volumeChartData() {
    if (volumeData.isEmpty) {
      return LineChartData(
        gridData: FlGridData(show: true),
        titlesData: FlTitlesData(show: true),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: Colors.grey.shade300),
        ),
      );
    }

    List<FlSpot> spots = volumeData
        .asMap()
        .entries
        .map((entry) => FlSpot(
              entry.key * 0.05,
              entry.value.value,
            ))
        .toList();

    double effectiveMinY = minY ?? 0.0;
    double effectiveMaxY = maxY ?? 6.0;

    if (useDynamicScaling && spots.isNotEmpty) {
      List<double> yValues = spots.map((s) => s.y).toList();
      double dataMinY = yValues.reduce((a, b) => a < b ? a : b);
      double dataMaxY = yValues.reduce((a, b) => a > b ? a : b);
      
      double yRange = (dataMaxY - dataMinY).abs();
      double yPadding = yRange * 0.15;
      
      effectiveMinY = dataMinY > 0 ? 0 : dataMinY - yPadding;
      effectiveMaxY = dataMaxY + yPadding;
    }

    double yRange = (effectiveMaxY - effectiveMinY).abs();
    double yInterval = yRange > 10 ? 2.0 : yRange > 5 ? 1.0 : yRange > 2 ? 0.5 : 0.2;
    
    double xRange = spots.isNotEmpty ? spots.last.x : 6.0;
    double xInterval = xRange > 10 ? 2.0 : xRange > 5 ? 1.0 : xRange > 2 ? 0.5 : 0.2;

    return LineChartData(
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        drawHorizontalLine: true,
        horizontalInterval: yInterval,
        verticalInterval: xInterval,
        getDrawingHorizontalLine: (value) {
          return FlLine(
            color: Colors.grey.shade200,
            strokeWidth: 0.8,
          );
        },
        getDrawingVerticalLine: (value) {
          return FlLine(
            color: Colors.grey.shade200,
            strokeWidth: 0.8,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 35,
            interval: xInterval,
            getTitlesWidget: (value, meta) {
              String label = xInterval < 1
                  ? value.toStringAsFixed(1)
                  : value.toInt().toString();
              return SideTitleWidget(
                axisSide: meta.axisSide,
                child: Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    label,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                      fontSize: 11,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: yInterval,
            reservedSize: 55,
            getTitlesWidget: (value, meta) {
              String label = yInterval < 1
                  ? value.toStringAsFixed(1)
                  : value.toInt().toString();
              return SideTitleWidget(
                axisSide: meta.axisSide,
                child: Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: Text(
                    label,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                      fontSize: 11,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: Colors.grey.shade300, width: 1),
      ),
      minX: 0,
      maxX: spots.isNotEmpty ? spots.last.x : 6.0,
      minY: effectiveMinY,
      maxY: effectiveMaxY,
      lineBarsData: [
        LineChartBarData(
          spots: spots,
          isCurved: true,
          color: Colors.green,
          barWidth: 2.5,
          isStrokeCapRound: true,
          dotData: const FlDotData(show: false),
          belowBarData: BarAreaData(
            show: true,
            color: Colors.green.withOpacity(0.12),
          ),
        ),
      ],
    );
  }
}
