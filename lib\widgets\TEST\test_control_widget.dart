import 'package:flutter/material.dart';
import 'package:befine/services/ble_test_service.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:befine/widgets/common/custom_button.dart';
import 'package:befine/widgets/common/loading_widget.dart';
import 'package:provider/provider.dart';

class TestControlWidget extends StatelessWidget {
  const TestControlWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<BleTestService>(
      builder: (context, testService, child) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(25),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withAlpha(30),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.play_circle_outline,
                      color: AppTheme.primaryColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Contrôle du Test',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                        Text(
                          'Gérez l\'exécution du test respiratoire',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Progress Section
              _buildProgressSection(testService),

              const SizedBox(height: 24),

              // Control Buttons
              _buildControlButtons(context, testService),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProgressSection(BleTestService testService) {
    // Calculate time-based progress (SAMPLE_INTERVAL = 50ms, MAX_SAMPLES = 128)
    const int sampleIntervalMs = 50;
    const int maxSamples = 128;
    double totalTestTimeSeconds =
        (maxSamples * sampleIntervalMs) / 1000.0; // 6.4 seconds
    double currentTimeSeconds =
        (testService.currentSample * sampleIntervalMs) / 1000.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Progression du Test',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withAlpha(20),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${testService.testProgress.toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Progress bar
          Container(
            height: 8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: Colors.grey.shade300,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: LinearProgressIndicator(
                value: testService.testProgress / 100.0,
                backgroundColor: Colors.transparent,
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppTheme.primaryColor,
                ),
                minHeight: 8,
              ),
            ),
          ),

          const SizedBox(height: 12),

          // Time and samples info
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Temps',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    '${currentTimeSeconds.toStringAsFixed(1)}s / ${totalTestTimeSeconds.toStringAsFixed(1)}s',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'Échantillons',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    '${testService.currentSample} / ${testService.maxSamples}',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
            ],
          ),

          // Status indicator
          if (testService.testStatus == 'measuring') ...[
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppTheme.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Mesure en cours - Respirez normalement',
                  style: TextStyle(
                    fontSize: 13,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildControlButtons(
    BuildContext context,
    BleTestService testService,
  ) {
    bool isConnected = testService.testStatus != 'disconnected';
    bool isMeasuring = testService.testStatus == 'measuring';
    bool isComplete = testService.testStatus == 'complete';
    bool canStart =
        isConnected && !isMeasuring && testService.testStatus != 'starting';
    bool canReset =
        isConnected && (testService.testStatus == 'stopped' || isComplete);

    return Column(
      children: [
        // Primary action button (Start/Stop)
        CustomButton(
          text: isMeasuring ? 'Arrêter le Test' : 'Démarrer le Test',
          icon: isMeasuring ? Icons.stop : Icons.play_arrow,
          onPressed:
              isMeasuring
                  ? () => _stopTest(context, testService)
                  : canStart
                  ? () => _startTest(context, testService)
                  : null,
          backgroundColor:
              isMeasuring ? Colors.red.shade600 : AppTheme.primaryColor,
          width: double.infinity,
          height: 56,
        ),

        const SizedBox(height: 12),

        // Secondary action button (Reset)
        CustomButton(
          text: 'Réinitialiser',
          icon: Icons.refresh,
          onPressed: canReset ? () => _resetTest(context, testService) : null,
          isOutlined: true,
          width: double.infinity,
          height: 48,
        ),
      ],
    );
  }

  Future<void> _startTest(
    BuildContext context,
    BleTestService testService,
  ) async {
    try {
      final success = await testService.startMeasurementCycle();
      if (!success && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Impossible de démarrer le test'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du démarrage: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _stopTest(
    BuildContext context,
    BleTestService testService,
  ) async {
    try {
      final success = await testService.stopMeasurementCycle();
      if (!success && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Impossible d\'arrêter le test'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'arrêt: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _resetTest(
    BuildContext context,
    BleTestService testService,
  ) async {
    try {
      final success = await testService.resetMeasurementCycle();
      if (!success && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Impossible de réinitialiser le test'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la réinitialisation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
