import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:befine/models/measurement_data.dart';
import 'package:befine/models/test_results.dart';
import 'package:befine/services/pdf_service.dart';
import 'package:befine/theme/app_theme.dart';

/// Widget to display test results in a card
class TestResultsCard extends StatefulWidget {
  /// Test results to display
  final TestResults results;

  /// Measurement data for the test
  final MeasurementData measurementData;

  /// Constructor
  const TestResultsCard({
    super.key,
    required this.results,
    required this.measurementData,
  });

  @override
  State<TestResultsCard> createState() => _TestResultsCardState();
}

class _TestResultsCardState extends State<TestResultsCard> {
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3,
      margin: const EdgeInsets.symmetric(vertical: 16.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      color: AppTheme.primaryColor.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(Icons.analytics, color: AppTheme.primaryColor, size: 28),
                const SizedBox(width: 8),
                Text(
                  'Résultats du Test',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
              ],
            ),

            const Divider(height: 24),
            // DIP (Débit Inspiratoire de Pointe)
            _buildResultRow(
              label: 'DIP (Débit Inspiratoire de Pointe)',
              value: '${(-widget.results.dip).toStringAsFixed(2)} L/s',
              isHighlighted: true,
              icon: Icons.trending_down,
              iconColor: Colors.blue,
            ),
            const SizedBox(height: 8),
            // Inhalation Duration
            _buildResultRow(
              label: 'Durée d\'Inspiration',
              value:
                  '${widget.results.inhalationDuration.toStringAsFixed(2)} s',
              isHighlighted: true,
              icon: Icons.timer,
              iconColor: Colors.green,
            ),
            const SizedBox(height: 8),
            // Inhalation Start/End
            _buildResultRow(
              label: 'Début/Fin d\'Inspiration',
              value:
                  '${widget.results.inhalationStartTime.toStringAsFixed(2)} s - ${widget.results.inhalationEndTime.toStringAsFixed(2)} s',
              icon: Icons.compare_arrows,
              iconColor: Colors.blue,
            ),
            const SizedBox(height: 8),
            // DIP Time
            _buildResultRow(
              label: 'Temps du DIP',
              value: '${widget.results.dipTime.toStringAsFixed(2)} s',
              icon: Icons.access_time,
              iconColor: Colors.orange,
            ),

            const SizedBox(height: 8),

            // Maximum Volume
            _buildResultRow(
              label: 'Volume Maximum',
              value: '${widget.results.maxVolume.toStringAsFixed(2)} L',
              icon: Icons.speed,
              iconColor: Colors.black,
            ),

            const SizedBox(height: 16),

            // PDF Generation Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _savePdf(context),
                  icon:
                      Platform.isAndroid
                          ? const Icon(Icons.share)
                          : const Icon(Icons.save_alt),
                  label: Text(
                    Platform.isAndroid ? 'Partager PDF' : 'Enregistrer PDF',
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _printPdf(context),
                  icon: const Icon(Icons.print),
                  label: const Text('Imprimer'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.secondaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Generate and save PDF
  Future<void> _savePdf(BuildContext initialContext) async {
    try {
      // Check if widget is still mounted
      if (!mounted) return;

      // Show loading indicator using the initial context
      _showLoadingDialog(
        initialContext,
        Platform.isAndroid
            ? 'Préparation du PDF pour partage...'
            : 'Génération du PDF en cours...',
      );

      // Generate PDF
      final pdfBytes = await _generatePdf();

      // Close loading dialog if still mounted
      if (mounted) {
        Navigator.of(initialContext).pop();
      } else {
        return; // Exit if widget is no longer mounted
      }

      if (pdfBytes == null) {
        if (!mounted) return;
        ScaffoldMessenger.of(initialContext).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de la génération du PDF'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Share PDF
      await Printing.sharePdf(
        bytes: pdfBytes,
        filename:
            'test_results_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.pdf',
      );

      if (!mounted) return;
      ScaffoldMessenger.of(initialContext).showSnackBar(
        const SnackBar(
          content: Text('PDF partagé avec succès'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Handle errors if still mounted
      if (mounted) {
        // Try to close any open dialogs
        try {
          if (Navigator.of(initialContext).canPop()) {
            Navigator.of(initialContext).pop();
          }
        } catch (_) {
          // Ignore errors when closing dialogs
        }

        // Show error message
        ScaffoldMessenger.of(initialContext).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la génération du PDF: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Generate and print PDF
  Future<void> _printPdf(BuildContext initialContext) async {
    try {
      // Check if widget is still mounted
      if (!mounted) return;

      // Show loading indicator using the initial context
      _showLoadingDialog(initialContext, 'Préparation de l\'impression...');

      // Generate PDF
      final pdfBytes = await _generatePdf();

      // Close loading dialog if still mounted
      if (mounted) {
        Navigator.of(initialContext).pop();
      } else {
        return; // Exit if widget is no longer mounted
      }

      if (pdfBytes == null) {
        if (!mounted) return;
        ScaffoldMessenger.of(initialContext).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de la génération du PDF'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Print PDF
      await Printing.layoutPdf(
        onLayout: (format) => pdfBytes,
        name: 'Test Results ${DateFormat('yyyy-MM-dd').format(DateTime.now())}',
      );
    } catch (e) {
      // Handle errors if still mounted
      if (mounted) {
        // Try to close any open dialogs
        try {
          if (Navigator.of(initialContext).canPop()) {
            Navigator.of(initialContext).pop();
          }
        } catch (_) {
          // Ignore errors when closing dialogs
        }

        // Show error message
        ScaffoldMessenger.of(initialContext).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'impression: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Generate PDF document
  Future<Uint8List?> _generatePdf() async {
    final pdf = pw.Document();

    try {
      // Add page to the PDF
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header
                pw.Header(
                  level: 0,
                  child: pw.Text('Rapport de Test Respiratoire'),
                ),
                pw.SizedBox(height: 20),

                // Date and time
                pw.Text(
                  'Date: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}',
                  style: pw.TextStyle(
                    fontSize: 12,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 20),

                // Results table
                pw.Table(
                  border: pw.TableBorder.all(),
                  children: [
                    // Header row
                    pw.TableRow(
                      decoration: const pw.BoxDecoration(
                        color: PdfColors.grey300,
                      ),
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            'Paramètre',
                            style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            'Valeur',
                            style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                          ),
                        ),
                      ],
                    ),
                    // DIP row
                    pw.TableRow(
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text('DIP (Débit Inspiratoire de Pointe)'),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            '${(-widget.results.dip).toStringAsFixed(2)} L/s',
                          ),
                        ),
                      ],
                    ),
                    // Inhalation Duration row
                    pw.TableRow(
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text('Durée d\'Inspiration'),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            '${widget.results.inhalationDuration.toStringAsFixed(2)} s',
                          ),
                        ),
                      ],
                    ),
                    // Maximum Volume row
                    pw.TableRow(
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text('Volume Maximum'),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(8),
                          child: pw.Text(
                            '${widget.results.maxVolume.toStringAsFixed(2)} L',
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                pw.SizedBox(height: 20),

                // Notes
                pw.Text(
                  'Notes:',
                  style: pw.TextStyle(
                    fontSize: 14,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 5),
                pw.Text(
                  'Ce rapport a été généré automatiquement par l\'application BeFine. '
                  'Les résultats doivent être interprétés par un professionnel de santé.',
                  style: const pw.TextStyle(fontSize: 10),
                ),
              ],
            );
          },
        ),
      );

      return pdf.save();
    } catch (e) {
      debugPrint('Error generating PDF: $e');
      return null;
    }
  }

  // Show loading dialog
  void _showLoadingDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Expanded(child: Text(message)),
            ],
          ),
        );
      },
    );
  }

  Widget _buildResultRow({
    required String label,
    required String value,
    bool isHighlighted = false,
    required IconData icon,
    required Color iconColor,
  }) {
    return Row(
      children: [
        Icon(icon, color: iconColor),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontSize: isHighlighted ? 16 : 14,
              fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isHighlighted ? 16 : 14,
            fontWeight: FontWeight.bold,
            color: isHighlighted ? iconColor : AppTheme.textPrimaryColor,
          ),
        ),
      ],
    );
  }
}
