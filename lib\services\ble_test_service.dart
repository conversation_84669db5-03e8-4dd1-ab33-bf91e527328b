import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:befine/models/measurement_data.dart';
import 'package:befine/services/ble_manager.dart';

/// Service for handling BLE test execution
class BleTestService extends ChangeNotifier {
  // BLE Manager instance
  final BleManager _bleManager;

  // Test status
  String _testStatus = "disconnected";
  String get testStatus => _testStatus;

  // Test progress
  double _testProgress = 0.0;
  double get testProgress => _testProgress;

  // Maximum samples
  int _maxSamples = 128;
  int get maxSamples => _maxSamples;

  // Current sample count
  int _currentSample = 0;
  int get currentSample => _currentSample;

  // Measurement data
  MeasurementData _measurementData = MeasurementData.empty();
  MeasurementData get measurementData => _measurementData;

  // BLE characteristics
  BluetoothCharacteristic? _dataCharacteristic;
  BluetoothCharacteristic? _statusCharacteristic;

  // Stream subscriptions
  StreamSubscription<List<int>>? _dataSubscription;
  StreamSubscription<List<int>>? _statusSubscription;

  // Stream controller for status updates
  final _statusStreamController = StreamController<String>.broadcast();
  Stream<String> get statusStream => _statusStreamController.stream;

  // Stream controller for measurement updates
  final _measurementStreamController =
      StreamController<MeasurementData>.broadcast();
  Stream<MeasurementData> get measurementStream =>
      _measurementStreamController.stream;

  /// Constructor
  BleTestService(this._bleManager) {
    // Listen to BLE manager connection state changes
    _bleManager.addListener(_onBleManagerChanged);
  }

  /// Handle BLE manager state changes
  void _onBleManagerChanged() {
    if (_bleManager.connectionState == BleConnectionState.connected) {
      _setupCharacteristics();
    } else if (_bleManager.connectionState == BleConnectionState.disconnected) {
      _handleDisconnection();
    }
    notifyListeners();
  }

  /// Set up characteristics when connected
  Future<void> _setupCharacteristics() async {
    if (_bleManager.connectionState != BleConnectionState.connected ||
        _bleManager.connectedDevice == null) {
      debugPrint('Cannot setup characteristics: device not connected');
      return;
    }

    try {
      // Get characteristics from BLE manager
      final characteristics =
          await _bleManager.getSmartInhalerCharacteristics();

      if (characteristics == null) {
        debugPrint('Failed to get SmartInhealer characteristics');
        return;
      }

      final dataCharacteristic = characteristics['data'];
      final statusCharacteristic = characteristics['status'];

      if (dataCharacteristic == null) {
        debugPrint('Data characteristic not found');
        return;
      }

      if (statusCharacteristic == null) {
        debugPrint('Status characteristic not found');
        return;
      }

      _dataCharacteristic = dataCharacteristic;
      _statusCharacteristic = statusCharacteristic;

      // Enable notifications for data characteristic
      await _dataCharacteristic!.setNotifyValue(true);

      // Enable notifications for status characteristic
      await _statusCharacteristic!.setNotifyValue(true);

      // Subscribe to data notifications
      _dataSubscription = _dataCharacteristic!.onValueReceived.listen(
        _handleIncomingData,
        onError: (error) {
          debugPrint('Error receiving data: $error');
        },
      );

      // Subscribe to status notifications
      _statusSubscription = _statusCharacteristic!.onValueReceived.listen(
        _handleStatusUpdate,
        onError: (error) {
          debugPrint('Error receiving status: $error');
        },
      );

      // Register for cleanup when disconnected
      _bleManager.connectedDevice!.cancelWhenDisconnected(
        _dataSubscription!,
        next: true,
      );
      _bleManager.connectedDevice!.cancelWhenDisconnected(
        _statusSubscription!,
        next: true,
      );

      // Update test status
      _testStatus = "ready";
      notifyListeners();

      debugPrint('BLE characteristics setup completed successfully');
    } catch (e) {
      debugPrint('Error setting up characteristics: $e');
    }
  }

  /// Handle incoming data from BLE
  void _handleIncomingData(List<int> data) {
    if (data.isEmpty) return;

    try {
      // Convert bytes to string
      String dataString = utf8.decode(data);
      debugPrint('Received data: $dataString');

      // Parse JSON data
      Map<String, dynamic> jsonData = jsonDecode(dataString);

      // Extract time, flow, and volume values
      double time = jsonData['time']?.toDouble() ?? 0.0;
      double flow = jsonData['flow']?.toDouble() ?? 0.0;
      double volume = jsonData['volume']?.toDouble() ?? 0.0;

      // Extract sample count and progress if available
      if (jsonData.containsKey('sample')) {
        _currentSample = jsonData['sample'];
      }

      if (jsonData.containsKey('progress')) {
        _testProgress = jsonData['progress'].toDouble();
      }

      // Update measurement data
      _measurementData = _measurementData
          .addFlowPoint(time, flow)
          .addVolumePoint(time, volume);

      // Notify listeners
      _measurementStreamController.add(_measurementData);
      notifyListeners();
    } catch (e) {
      debugPrint('Error parsing data: $e');
    }
  }

  /// Handle status updates from the device
  void _handleStatusUpdate(List<int> data) {
    if (data.isEmpty) return;

    try {
      // Convert bytes to string
      String dataString = utf8.decode(data);
      debugPrint('Received status: $dataString');

      // Parse JSON data
      Map<String, dynamic> jsonData = jsonDecode(dataString);

      // Extract status
      String status = jsonData['status'] ?? 'unknown';
      _testStatus = status;

      // Extract sample count if available
      if (jsonData.containsKey('samples')) {
        _currentSample = jsonData['samples'];
      }

      // Extract max samples if available
      if (jsonData.containsKey('maxSamples')) {
        _maxSamples = jsonData['maxSamples'];
      }

      // Extract progress if available
      if (jsonData.containsKey('progress')) {
        _testProgress = jsonData['progress'].toDouble();
      } else if (_maxSamples > 0 && _currentSample > 0) {
        // Calculate progress if not provided
        _testProgress = (_currentSample / _maxSamples) * 100.0;
      }

      // Notify listeners
      _statusStreamController.add(status);
      notifyListeners();
    } catch (e) {
      debugPrint('Error parsing status data: $e');
    }
  }

  /// Handle disconnection
  void _handleDisconnection() {
    // Cancel all subscriptions
    _dataSubscription?.cancel();
    _dataSubscription = null;

    _statusSubscription?.cancel();
    _statusSubscription = null;

    // Clear all references
    _dataCharacteristic = null;
    _statusCharacteristic = null;

    // Reset status
    _testStatus = "disconnected";
    _testProgress = 0.0;
    _currentSample = 0;

    notifyListeners();
  }

  /// Start a new measurement cycle
  Future<bool> startMeasurementCycle() async {
    if (_bleManager.connectionState != BleConnectionState.connected ||
        _dataCharacteristic == null) {
      debugPrint(
        'Cannot start measurement: device not connected or characteristic not found',
      );
      return false;
    }

    try {
      // Clear existing data
      _measurementData = _measurementData.clear();
      _measurementStreamController.add(_measurementData);

      // Reset test progress
      _testProgress = 0.0;
      _currentSample = 0;
      _testStatus = "measuring";
      notifyListeners();

      // Send command to device to start measurement
      await _sendCommand('START_MEASUREMENT');
      debugPrint('Measurement cycle started successfully');
      return true;
    } catch (e) {
      debugPrint('Error starting measurement: $e');
      return false;
    }
  }

  /// Auto-start measurement when the test page is opened
  /// Returns true if auto-start was successful, false otherwise
  Future<bool> autoStartMeasurement() async {
    // Only auto-start if we're connected and not already measuring
    if (_bleManager.connectionState == BleConnectionState.connected &&
        _testStatus != "measuring" &&
        _dataCharacteristic != null) {
      debugPrint('Auto-starting measurement...');

      // Wait a moment to ensure all characteristics are set up
      await Future.delayed(const Duration(milliseconds: 500));

      // Start the measurement
      return await startMeasurementCycle();
    }

    return false;
  }

  /// Stop the current measurement cycle
  Future<bool> stopMeasurementCycle() async {
    if (_bleManager.connectionState != BleConnectionState.connected ||
        _dataCharacteristic == null) {
      debugPrint(
        'Cannot stop measurement: device not connected or characteristic not found',
      );
      return false;
    }

    try {
      // Send command to device to stop measurement
      await _sendCommand('STOP_MEASUREMENT');
      _testStatus = "stopped";
      notifyListeners();
      debugPrint('Measurement cycle stopped successfully');
      return true;
    } catch (e) {
      debugPrint('Error stopping measurement: $e');
      return false;
    }
  }

  /// Reset the measurement data
  Future<bool> resetMeasurementCycle() async {
    if (_bleManager.connectionState != BleConnectionState.connected ||
        _dataCharacteristic == null) {
      debugPrint(
        'Cannot reset measurement: device not connected or characteristic not found',
      );
      return false;
    }

    try {
      // Clear existing data
      _measurementData = _measurementData.clear();
      _measurementStreamController.add(_measurementData);

      // Reset test progress
      _testProgress = 0.0;
      _currentSample = 0;
      _testStatus = "ready";
      notifyListeners();

      // Send command to device to reset measurement
      await _sendCommand('RESET_MEASUREMENT');
      debugPrint('Measurement cycle reset successfully');
      return true;
    } catch (e) {
      debugPrint('Error resetting measurement: $e');
      return false;
    }
  }

  /// Send a command to the device
  Future<void> _sendCommand(String command) async {
    if (_dataCharacteristic == null) {
      debugPrint('Cannot send command: characteristic is null');
      return;
    }

    try {
      // Write with response to ensure delivery
      await _dataCharacteristic!
          .write(utf8.encode(command), withoutResponse: false)
          .timeout(
            const Duration(seconds: 5),
            onTimeout: () {
              throw TimeoutException('Command write timed out');
            },
          );

      debugPrint('Command sent: $command');
    } catch (e) {
      debugPrint('Error sending command: $e');
      rethrow; // Propagate error to caller
    }
  }

  @override
  void dispose() {
    // Cancel all subscriptions
    _dataSubscription?.cancel();
    _statusSubscription?.cancel();

    // Close stream controllers
    _measurementStreamController.close();
    _statusStreamController.close();

    // Remove listener from BLE manager
    _bleManager.removeListener(_onBleManagerChanged);

    debugPrint('BleTestService disposed');
    super.dispose();
  }
}
