import 'package:flutter/material.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';
import 'package:befine/theme/app_theme.dart';

class PatientBottomNavbar extends StatelessWidget {
  final PersistentTabController controller;

  const PatientBottomNavbar({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return PersistentTabView(
      context,
      controller: controller,
      backgroundColor: Colors.white,
      handleAndroidBackButtonPress: true,
      resizeToAvoidBottomInset: true,
      stateManagement: true,
      hideNavigationBarWhenKeyboardAppears: true,
      decoration: NavBarDecoration(
        borderRadius: BorderRadius.circular(0),
        colorBehindNavBar: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      navBarStyle: NavBarStyle.style15,
      items: _buildNavBarItems(),
    );
  }

  List<PersistentBottomNavBarItem> _buildNavBarItems() {
    return [
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.home),
        title: "Accueil",
        activeColorPrimary: AppTheme.primaryColor,
        inactiveColorPrimary: Colors.grey,
        textStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.science),
        title: "Test",
        activeColorPrimary: AppTheme.primaryColor,
        inactiveColorPrimary: Colors.grey,
        textStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.history),
        title: "Historique",
        activeColorPrimary: AppTheme.primaryColor,
        inactiveColorPrimary: Colors.grey,
        textStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.calendar_today),
        title: "Rendez-vous",
        activeColorPrimary: AppTheme.primaryColor,
        inactiveColorPrimary: Colors.grey,
        textStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
      PersistentBottomNavBarItem(
        icon: const Icon(Icons.person),
        title: "Profil",
        activeColorPrimary: AppTheme.primaryColor,
        inactiveColorPrimary: Colors.grey,
        textStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    ];
  }
}
