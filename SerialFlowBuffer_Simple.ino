#include "BLEDevice.h"
#include "BLEServer.h"
#include "BLEUtils.h"
#include "BLE2902.h"

// BLE Service and Characteristic UUIDs (must match Flutter app)
#define SERVICE_UUID        "4fafc201-1fb5-459e-8fcc-c5c9c331914b"
#define DATA_CHAR_UUID      "beb5483e-36e1-4688-b7f5-ea07361b26a8"
#define STATUS_CHAR_UUID    "beb5483e-36e1-4688-b7f5-ea07361b26a9"

// Device name
#define DEVICE_NAME "SmartInhealer"

// BLE objects
BLEServer* pServer = NULL;
BLECharacteristic* pDataCharacteristic = NULL;
BLECharacteristic* pStatusCharacteristic = NULL;
bool deviceConnected = false;
bool oldDeviceConnected = false;

// Battery monitoring
const int BATTERY_PIN = 5; // GPIO5 (ADC pin)
const float BATTERY_MAX_VOLTAGE = 4.2; // Maximum battery voltage
const float BATTERY_MIN_VOLTAGE = 3.0; // Minimum battery voltage

// Test data simulation
bool isTestRunning = false;
unsigned long testStartTime = 0;
int sampleCount = 0;
const int MAX_SAMPLES = 100;

// Function declarations
void startMeasurement();
void stopMeasurement();
void resetMeasurement();
void handleTestExecution();
void sendMeasurementData(float time, float flow, float volume, int sample);
void sendStatusUpdate(String status, int samples, int maxSamples);
void sendBatteryUpdate();
int getBatteryLevel();

class MyServerCallbacks: public BLEServerCallbacks {
    void onConnect(BLEServer* pServer) {
      deviceConnected = true;
      Serial.println("Device connected");
    };

    void onDisconnect(BLEServer* pServer) {
      deviceConnected = false;
      Serial.println("Device disconnected");
    }
};

class MyDataCallbacks: public BLECharacteristicCallbacks {
    void onWrite(BLECharacteristic *pCharacteristic) {
      String rxValue = pCharacteristic->getValue().c_str();

      if (rxValue.length() > 0) {
        Serial.print("Received command: ");
        Serial.println(rxValue);

        // Handle commands
        if (rxValue == "START_MEASUREMENT") {
          startMeasurement();
        } else if (rxValue == "STOP_MEASUREMENT") {
          stopMeasurement();
        } else if (rxValue == "RESET_MEASUREMENT") {
          resetMeasurement();
        }
      }
    }
};

void setup() {
  Serial.begin(115200);
  Serial.println("Starting SmartInhealer BLE Server...");

  // Initialize BLE
  BLEDevice::init(DEVICE_NAME);
  pServer = BLEDevice::createServer();
  pServer->setCallbacks(new MyServerCallbacks());

  // Create BLE Service
  BLEService *pService = pServer->createService(SERVICE_UUID);

  // Create Data Characteristic
  pDataCharacteristic = pService->createCharacteristic(
                         DATA_CHAR_UUID,
                         BLECharacteristic::PROPERTY_READ |
                         BLECharacteristic::PROPERTY_WRITE |
                         BLECharacteristic::PROPERTY_NOTIFY
                       );
  pDataCharacteristic->setCallbacks(new MyDataCallbacks());
  pDataCharacteristic->addDescriptor(new BLE2902());

  // Create Status Characteristic
  pStatusCharacteristic = pService->createCharacteristic(
                           STATUS_CHAR_UUID,
                           BLECharacteristic::PROPERTY_READ |
                           BLECharacteristic::PROPERTY_NOTIFY
                         );
  pStatusCharacteristic->addDescriptor(new BLE2902());

  // Start the service
  pService->start();

  // Start advertising
  BLEAdvertising *pAdvertising = BLEDevice::getAdvertising();
  pAdvertising->addServiceUUID(SERVICE_UUID);
  pAdvertising->setScanResponse(false);
  pAdvertising->setMinPreferred(0x0);  // set value to 0x00 to not advertise this parameter
  BLEDevice::startAdvertising();

  Serial.println("SmartInhealer BLE Server started!");
  Serial.println("Waiting for client connection...");

  // Send initial status
  sendStatusUpdate("ready", 0, 0);
}

void loop() {
  // Handle connection state changes
  if (!deviceConnected && oldDeviceConnected) {
    delay(500); // give the bluetooth stack the chance to get things ready
    pServer->startAdvertising(); // restart advertising
    Serial.println("Start advertising");
    oldDeviceConnected = deviceConnected;
  }

  if (deviceConnected && !oldDeviceConnected) {
    oldDeviceConnected = deviceConnected;
    Serial.println("Device connected - sending initial status");
    sendStatusUpdate("ready", 0, 0);
  }

  // Handle test execution
  if (isTestRunning && deviceConnected) {
    handleTestExecution();
  }

  // Send periodic battery updates
  static unsigned long lastBatteryUpdate = 0;
  if (deviceConnected && millis() - lastBatteryUpdate > 30000) { // Every 30 seconds
    sendBatteryUpdate();
    lastBatteryUpdate = millis();
  }

  delay(100);
}

void startMeasurement() {
  if (!deviceConnected) return;

  Serial.println("Starting measurement...");
  isTestRunning = true;
  testStartTime = millis();
  sampleCount = 0;

  sendStatusUpdate("measuring", sampleCount, MAX_SAMPLES);
}

void stopMeasurement() {
  if (!deviceConnected) return;

  Serial.println("Stopping measurement...");
  isTestRunning = false;

  sendStatusUpdate("stopped", sampleCount, MAX_SAMPLES);
}

void resetMeasurement() {
  if (!deviceConnected) return;

  Serial.println("Resetting measurement...");
  isTestRunning = false;
  sampleCount = 0;

  sendStatusUpdate("ready", 0, 0);
}

void handleTestExecution() {
  static unsigned long lastSample = 0;

  if (millis() - lastSample > 100) { // Send sample every 100ms
    if (sampleCount < MAX_SAMPLES) {
      // Generate simulated flow and volume data
      float time = (millis() - testStartTime) / 1000.0;
      float flow = sin(time * 2) * 50 + 50; // Simulated flow data
      float volume = time * 10; // Simulated volume data

      // Send measurement data
      sendMeasurementData(time, flow, volume, sampleCount + 1);

      sampleCount++;

      // Update progress
      float progress = (float)sampleCount / MAX_SAMPLES * 100.0;
      sendStatusUpdate("measuring", sampleCount, MAX_SAMPLES);

      lastSample = millis();
    } else {
      // Test complete
      isTestRunning = false;
      sendStatusUpdate("complete", sampleCount, MAX_SAMPLES);
      Serial.println("Test completed!");
    }
  }
}

void sendMeasurementData(float time, float flow, float volume, int sample) {
  if (!deviceConnected || !pDataCharacteristic) return;

  // Create JSON data
  String jsonData = "{";
  jsonData += "\"time\":";
  jsonData += String(time, 2);
  jsonData += ",\"flow\":";
  jsonData += String(flow, 2);
  jsonData += ",\"volume\":";
  jsonData += String(volume, 2);
  jsonData += ",\"sample\":";
  jsonData += String(sample);
  jsonData += "}";

  pDataCharacteristic->setValue(jsonData.c_str());
  pDataCharacteristic->notify();

  Serial.print("Sent: ");
  Serial.println(jsonData);
}

void sendStatusUpdate(String status, int samples, int maxSamples) {
  if (!deviceConnected || !pStatusCharacteristic) return;

  int batteryLevel = getBatteryLevel();
  float progress = maxSamples > 0 ? (float)samples / maxSamples * 100.0 : 0.0;

  // Create JSON status
  String jsonStatus = "{";
  jsonStatus += "\"status\":\"";
  jsonStatus += status;
  jsonStatus += "\",\"samples\":";
  jsonStatus += String(samples);
  jsonStatus += ",\"maxSamples\":";
  jsonStatus += String(maxSamples);
  jsonStatus += ",\"progress\":";
  jsonStatus += String(progress, 1);
  jsonStatus += ",\"battery\":";
  jsonStatus += String(batteryLevel);
  jsonStatus += "}";

  pStatusCharacteristic->setValue(jsonStatus.c_str());
  pStatusCharacteristic->notify();

  Serial.print("Status: ");
  Serial.println(jsonStatus);
}

void sendBatteryUpdate() {
  if (!deviceConnected) return;

  int batteryLevel = getBatteryLevel();
  Serial.print("Battery level: ");
  Serial.print(batteryLevel);
  Serial.println("%");

  // Send battery update via status characteristic
  sendStatusUpdate(isTestRunning ? "measuring" : "ready", sampleCount, MAX_SAMPLES);
}

int getBatteryLevel() {
  // Read battery voltage from ADC
  int adcValue = analogRead(BATTERY_PIN);
  float voltage = (adcValue / 4095.0) * 3.3 * 2; // Assuming voltage divider

  // Convert to percentage
  float percentage = ((voltage - BATTERY_MIN_VOLTAGE) / (BATTERY_MAX_VOLTAGE - BATTERY_MIN_VOLTAGE)) * 100.0;

  // Clamp between 0 and 100
  if (percentage < 0) percentage = 0;
  if (percentage > 100) percentage = 100;

  return (int)percentage;
}
