#include <Arduino.h>
#include <BLEDevice.h>
#include <BLEServer.h>
#include <BLEUtils.h>
#include <BLE2902.h>
#include <ArduinoJson.h>

// BLE Configuration - MUST match Flutter app exactly
#define SERVICE_UUID        "4fafc201-1fb5-459e-8fcc-c5c9c331914b"
#define DATA_CHAR_UUID      "beb5483e-36e1-4688-b7f5-ea07361b26a8"
#define STATUS_CHAR_UUID    "beb5483e-36e1-4688-b7f5-ea07361b26a9"
#define DEVICE_NAME         "SmartInhealer"

// Debug mode - set to true for detailed logging
#define DEBUG_MODE          true

// Hardware Configuration
const int FLOW_SENSOR_PIN = 2;  // GPIO2 for flow sensor (interrupt capable)
const int BATTERY_PIN = 5;      // GPIO5 (ADC pin) for battery monitoring
const int LED_PIN = 8;          // GPIO8 for status LED

// BLE objects
BLEServer* pServer = NULL;
BLECharacteristic* pDataCharacteristic = NULL;
BLECharacteristic* pStatusCharacteristic = NULL;
bool deviceConnected = false;
bool oldDeviceConnected = false;

// Flow measurement variables
volatile unsigned long pulseCount = 0;
unsigned long lastTime = 0;
float flowRate = 0.0;
float totalVolume = 0.0;
const float calibrationFactor = 7.5; // Pulses per liter per minute

// Test control variables
bool isTestRunning = false;
unsigned long testStartTime = 0;
int sampleCount = 0;
const int MAX_SAMPLES = 128;
const int SAMPLE_INTERVAL = 100; // milliseconds

// Battery monitoring
const float BATTERY_MAX_VOLTAGE = 4.2;
const float BATTERY_MIN_VOLTAGE = 3.0;

// Function declarations
void IRAM_ATTR pulseCounter();
void sendMeasurementData();
void sendStatusUpdate(String status, int samples, int maxSamples);
void sendBatteryUpdate();
int getBatteryLevel();
void startMeasurement();
void stopMeasurement();
void resetMeasurement();
void handleTestExecution();

// BLE Server Callbacks
class MyServerCallbacks: public BLEServerCallbacks {
    void onConnect(BLEServer* pServer) {
      deviceConnected = true;
      digitalWrite(LED_PIN, HIGH);

      if (DEBUG_MODE) {
        Serial.println("=== CLIENT CONNECTED ===");
        Serial.println("Device connected successfully!");
        Serial.println("LED turned ON");
      }

      // Send initial status when connected
      delay(1000);
      sendStatusUpdate("ready", 0, 0);

      if (DEBUG_MODE) {
        Serial.println("Initial status sent to client");
        Serial.println("========================");
      }
    };

    void onDisconnect(BLEServer* pServer) {
      deviceConnected = false;
      digitalWrite(LED_PIN, LOW);

      if (DEBUG_MODE) {
        Serial.println("=== CLIENT DISCONNECTED ===");
        Serial.println("Device disconnected!");
        Serial.println("LED turned OFF");
      }

      // Stop any running test
      if (isTestRunning) {
        stopMeasurement();
        if (DEBUG_MODE) {
          Serial.println("Stopped running measurement");
        }
      }

      // Restart advertising
      delay(500);
      pServer->startAdvertising();

      if (DEBUG_MODE) {
        Serial.println("Advertising restarted - device discoverable again");
        Serial.println("============================");
      }
    }
};

// BLE Characteristic Callbacks
class MyDataCallbacks: public BLECharacteristicCallbacks {
    void onWrite(BLECharacteristic *pCharacteristic) {
      String rxValue = pCharacteristic->getValue().c_str();

      if (rxValue.length() > 0) {
        Serial.print("Received command: ");
        Serial.println(rxValue);

        // Handle commands
        if (rxValue == "START_MEASUREMENT") {
          startMeasurement();
        } else if (rxValue == "STOP_MEASUREMENT") {
          stopMeasurement();
        } else if (rxValue == "RESET_MEASUREMENT") {
          resetMeasurement();
        } else if (rxValue == "GET_STATUS") {
          sendStatusUpdate(isTestRunning ? "measuring" : "ready", sampleCount, MAX_SAMPLES);
        } else if (rxValue == "GET_BATTERY") {
          sendBatteryUpdate();
        }
      }
    }
};

// Interrupt service routine for flow sensor
void IRAM_ATTR pulseCounter() {
  pulseCount++;
}

void setup() {
  Serial.begin(115200);
  Serial.println("Starting SmartInhealer BLE Server...");

  // Initialize hardware
  pinMode(LED_PIN, OUTPUT);
  pinMode(FLOW_SENSOR_PIN, INPUT_PULLUP);
  digitalWrite(LED_PIN, LOW);

  // Attach interrupt for flow sensor
  attachInterrupt(digitalPinToInterrupt(FLOW_SENSOR_PIN), pulseCounter, FALLING);

  // Initialize BLE with device name
  BLEDevice::init(DEVICE_NAME);
  pServer = BLEDevice::createServer();
  pServer->setCallbacks(new MyServerCallbacks());

  // Create BLE Service
  BLEService *pService = pServer->createService(SERVICE_UUID);

  // Create Data Characteristic (Read/Write/Notify)
  pDataCharacteristic = pService->createCharacteristic(
                         DATA_CHAR_UUID,
                         BLECharacteristic::PROPERTY_READ |
                         BLECharacteristic::PROPERTY_WRITE |
                         BLECharacteristic::PROPERTY_NOTIFY
                       );
  pDataCharacteristic->setCallbacks(new MyDataCallbacks());
  pDataCharacteristic->addDescriptor(new BLE2902());

  // Create Status Characteristic (Read/Notify)
  pStatusCharacteristic = pService->createCharacteristic(
                           STATUS_CHAR_UUID,
                           BLECharacteristic::PROPERTY_READ |
                           BLECharacteristic::PROPERTY_NOTIFY
                         );
  pStatusCharacteristic->addDescriptor(new BLE2902());

  // Start the service
  pService->start();

  // Configure and start advertising
  BLEAdvertising *pAdvertising = BLEDevice::getAdvertising();

  // Add service UUID to advertisement
  pAdvertising->addServiceUUID(SERVICE_UUID);

  // Enable scan response to include more data
  pAdvertising->setScanResponse(true);

  // Set advertising parameters for better discoverability
  pAdvertising->setMinPreferred(0x06);  // 7.5ms
  pAdvertising->setMaxPreferred(0x12);  // 22.5ms

  // Start advertising
  BLEDevice::startAdvertising();

  if (DEBUG_MODE) {
    Serial.println("=== SmartInhealer BLE Server Started ===");
    Serial.println("Device name: " + String(DEVICE_NAME));
    Serial.println("Service UUID: " + String(SERVICE_UUID));
    Serial.println("Data Characteristic: " + String(DATA_CHAR_UUID));
    Serial.println("Status Characteristic: " + String(STATUS_CHAR_UUID));
    Serial.println("Advertising started - device should be discoverable");
    Serial.println("Waiting for client connection...");
    Serial.println("==========================================");
  }

  // Send initial status
  delay(1000);  // Give time for setup
  sendStatusUpdate("ready", 0, 0);
}

void loop() {
  // Handle connection state changes
  if (!deviceConnected && oldDeviceConnected) {
    delay(500);
    pServer->startAdvertising();
    Serial.println("Start advertising");
    oldDeviceConnected = deviceConnected;
  }

  if (deviceConnected && !oldDeviceConnected) {
    oldDeviceConnected = deviceConnected;
    Serial.println("Device connected - sending initial status");
    sendStatusUpdate("ready", 0, 0);
  }

  // Handle test execution
  if (isTestRunning && deviceConnected) {
    handleTestExecution();
  }

  // Send periodic battery updates
  static unsigned long lastBatteryUpdate = 0;
  if (deviceConnected && millis() - lastBatteryUpdate > 30000) { // Every 30 seconds
    sendBatteryUpdate();
    lastBatteryUpdate = millis();
  }

  delay(50);
}

void startMeasurement() {
  if (!deviceConnected) return;

  Serial.println("Starting measurement...");
  isTestRunning = true;
  testStartTime = millis();
  sampleCount = 0;
  totalVolume = 0.0;
  pulseCount = 0;
  lastTime = millis();

  sendStatusUpdate("measuring", sampleCount, MAX_SAMPLES);
}

void stopMeasurement() {
  if (!deviceConnected) return;

  Serial.println("Stopping measurement...");
  isTestRunning = false;

  sendStatusUpdate("stopped", sampleCount, MAX_SAMPLES);
}

void resetMeasurement() {
  if (!deviceConnected) return;

  Serial.println("Resetting measurement...");
  isTestRunning = false;
  sampleCount = 0;
  totalVolume = 0.0;
  pulseCount = 0;

  sendStatusUpdate("ready", 0, 0);
}

void handleTestExecution() {
  static unsigned long lastSample = 0;

  if (millis() - lastSample >= SAMPLE_INTERVAL) {
    if (sampleCount < MAX_SAMPLES) {
      // Calculate flow rate
      unsigned long currentTime = millis();
      unsigned long deltaTime = currentTime - lastTime;

      if (deltaTime >= 1000) { // Calculate every second
        // Disable interrupts while reading pulse count
        noInterrupts();
        unsigned long currentPulses = pulseCount;
        pulseCount = 0;
        interrupts();

        // Calculate flow rate (L/min)
        flowRate = (currentPulses / calibrationFactor);

        // Calculate volume increment (L)
        float volumeIncrement = (flowRate / 60.0) * (deltaTime / 1000.0);
        totalVolume += volumeIncrement;

        lastTime = currentTime;
      }

      // Send measurement data
      sendMeasurementData();

      sampleCount++;

      // Update progress
      sendStatusUpdate("measuring", sampleCount, MAX_SAMPLES);

      lastSample = millis();
    } else {
      // Test complete
      isTestRunning = false;
      sendStatusUpdate("complete", sampleCount, MAX_SAMPLES);
      Serial.println("Test completed!");
    }
  }
}

void sendMeasurementData() {
  if (!deviceConnected || !pDataCharacteristic) return;

  float time = (millis() - testStartTime) / 1000.0;

  // Create JSON data
  StaticJsonDocument<200> doc;
  doc["time"] = time;
  doc["flow"] = flowRate;
  doc["volume"] = totalVolume;
  doc["sample"] = sampleCount + 1;
  doc["progress"] = (float)(sampleCount + 1) / MAX_SAMPLES * 100.0;

  String jsonString;
  serializeJson(doc, jsonString);

  pDataCharacteristic->setValue(jsonString.c_str());
  pDataCharacteristic->notify();

  Serial.println("Sent: " + jsonString);
}

void sendStatusUpdate(String status, int samples, int maxSamples) {
  if (!deviceConnected || !pStatusCharacteristic) return;

  int batteryLevel = getBatteryLevel();
  float progress = maxSamples > 0 ? (float)samples / maxSamples * 100.0 : 0.0;

  // Create JSON status
  StaticJsonDocument<200> doc;
  doc["status"] = status;
  doc["samples"] = samples;
  doc["maxSamples"] = maxSamples;
  doc["progress"] = progress;
  doc["battery"] = batteryLevel;
  doc["timestamp"] = millis();

  String jsonString;
  serializeJson(doc, jsonString);

  pStatusCharacteristic->setValue(jsonString.c_str());
  pStatusCharacteristic->notify();

  Serial.println("Status: " + jsonString);
}

void sendBatteryUpdate() {
  if (!deviceConnected) return;

  int batteryLevel = getBatteryLevel();
  Serial.println("Battery level: " + String(batteryLevel) + "%");

  // Send battery update via status characteristic
  sendStatusUpdate(isTestRunning ? "measuring" : "ready", sampleCount, MAX_SAMPLES);
}

int getBatteryLevel() {
  // Read battery voltage from ADC
  int adcValue = analogRead(BATTERY_PIN);
  float voltage = (adcValue / 4095.0) * 3.3 * 2; // Assuming voltage divider

  // Convert to percentage
  float percentage = ((voltage - BATTERY_MIN_VOLTAGE) / (BATTERY_MAX_VOLTAGE - BATTERY_MIN_VOLTAGE)) * 100.0;

  // Clamp between 0 and 100
  if (percentage < 0) percentage = 0;
  if (percentage > 100) percentage = 100;

  return (int)percentage;
}
