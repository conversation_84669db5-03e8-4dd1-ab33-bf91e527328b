import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:persistent_bottom_nav_bar/persistent_bottom_nav_bar.dart';

import 'package:befine/pages/welcom/onboarding_screen.dart';
import 'package:befine/pages/welcom/welcome_screen.dart';

import 'package:befine/pages/sign/sign_in_screen.dart';
import 'package:befine/pages/sign/signup_doctor_screen.dart';
import 'package:befine/pages/sign/signup_patient_screen.dart';

import 'package:befine/pages/patient/patient_main_page.dart';
import 'package:befine/pages/patient/manage_devices_screen.dart';
import 'package:befine/pages/doctor/doctor_main_page.dart';

import 'package:befine/services/ble_manager.dart';
import 'package:befine/theme/app_theme.dart';

void main() {
  runApp(const MainApp());
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [ChangeNotifierProvider(create: (context) => BleManager())],
      child: MaterialApp(
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: AppTheme.primaryColor),
          useMaterial3: true,
          scaffoldBackgroundColor: AppTheme.backgroundColor,
          appBarTheme: AppBarTheme(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
          ),
        ),
        initialRoute: '/',
        routes: {
          '/': (context) => const OnboardingScreen(),
          '/welcome': (context) => const WelcomeScreen(),
          '/sign-in': (context) => const SignInScreen(),
          '/sign-up-doctor': (context) => const SignUpDoctorScreen(),
          '/sign-up-patient': (context) => const SignUpPatientScreen(),
          '/PatientMain': (context) => const PatientMainPage(),
          '/DoctorMainPage': (context) => const DoctorMainPage(),
          '/manage-devices': (context) => const ManageDevicesScreen(),
        },
      ),
    );
  }
}
