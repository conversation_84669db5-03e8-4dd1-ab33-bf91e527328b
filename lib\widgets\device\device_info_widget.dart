import 'dart:async';
import 'package:flutter/material.dart';
import 'package:befine/models/device_model.dart';
import 'package:befine/services/ble_manager.dart';
import 'package:befine/services/device_service.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:provider/provider.dart';

/// Widget for displaying device information without connection controls
class DeviceInfoWidget extends StatefulWidget {
  const DeviceInfoWidget({super.key});

  @override
  State<DeviceInfoWidget> createState() => _DeviceInfoWidgetState();
}

class _DeviceInfoWidgetState extends State<DeviceInfoWidget> {
  int? _batteryLevel;
  Timer? _batteryCheckTimer;

  @override
  void initState() {
    super.initState();
    _checkBatteryLevel();
    _batteryCheckTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => _checkBatteryLevel(),
    );
  }

  @override
  void dispose() {
    _batteryCheckTimer?.cancel();
    super.dispose();
  }

  Future<void> _checkBatteryLevel() async {
    final bleManager = Provider.of<BleManager>(context, listen: false);
    if (bleManager.connectionState == BleConnectionState.connected) {
      final batteryLevel = await bleManager.getDeviceBatteryLevel();
      if (mounted) {
        setState(() {
          _batteryLevel = batteryLevel;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<BleManager, DeviceService>(
      builder: (context, bleManager, deviceService, child) {
        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(25),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withAlpha(30),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.devices,
                      color: AppTheme.primaryColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Appareil Connecté',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                        Row(
                          children: [
                            _buildStatusIndicator(bleManager.connectionState),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              _buildDeviceInfo(bleManager, deviceService),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatusIndicator(BleConnectionState state) {
    Color color;
    String text;

    switch (state) {
      case BleConnectionState.connected:
        color = Colors.green;
        text = 'Connecté';
        break;
      case BleConnectionState.connecting:
        color = Colors.orange;
        text = 'Connexion en cours';
        break;
      case BleConnectionState.scanning:
        color = Colors.blue;
        text = 'Recherche';
        break;
      case BleConnectionState.error:
        color = Colors.red;
        text = 'Erreur';
        break;
      case BleConnectionState.disconnected:
        color = Colors.grey;
        text = 'Déconnecté';
        break;
    }

    return Row(
      children: [
        Container(
          width: 10,
          height: 10,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        const SizedBox(width: 8),
        Text(
          text,
          style: TextStyle(
            color: AppTheme.textSecondaryColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildDeviceInfo(BleManager bleManager, DeviceService deviceService) {
    if (bleManager.connectionState == BleConnectionState.connected &&
        bleManager.connectedDevice != null) {
      final device = bleManager.connectedDevice!;
      final deviceId = device.remoteId.toString();

      final savedDevice = deviceService.getDeviceById(deviceId);
      final deviceName = savedDevice?.customName ??
          (device.platformName.isNotEmpty
              ? device.platformName
              : 'SmartInhealer');

      final BatteryStatus batteryStatus = Device.getBatteryStatus(
        _batteryLevel ?? 0,
      );

      IconData batteryIcon;
      Color batteryColor;

      switch (batteryStatus) {
        case BatteryStatus.low:
          batteryIcon = Icons.battery_alert;
          batteryColor = Colors.red;
          break;
        case BatteryStatus.medium:
          batteryIcon = Icons.battery_3_bar;
          batteryColor = Colors.orange;
          break;
        case BatteryStatus.high:
          batteryIcon = Icons.battery_5_bar;
          batteryColor = Colors.green;
          break;
        case BatteryStatus.full:
          batteryIcon = Icons.battery_full;
          batteryColor = Colors.green;
          break;
        case BatteryStatus.unknown:
          batteryIcon = Icons.battery_unknown;
          batteryColor = Colors.grey;
          break;
      }

      return Column(
        children: [
          _buildInfoRow(
            Icons.bluetooth_connected,
            'Nom de l\'appareil',
            deviceName,
            AppTheme.primaryColor,
          ),
          const SizedBox(height: 12),
          _buildInfoRow(
            Icons.info_outline,
            'ID de l\'appareil',
            deviceId.substring(deviceId.length - 8),
            AppTheme.secondaryColor,
          ),
          const SizedBox(height: 12),
          _buildInfoRow(
            batteryIcon,
            'Niveau de batterie',
            _batteryLevel != null ? '$_batteryLevel%' : '--',
            batteryColor,
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                Navigator.of(context).pushNamed('/manage-devices');
              },
              icon: const Icon(Icons.settings, size: 20),
              label: const Text(
                'Gérer les appareils',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.primaryColor,
                padding: const EdgeInsets.symmetric(vertical: 16),
                side: BorderSide(
                  color: AppTheme.primaryColor,
                  width: 2,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      );
    } else {
      return Column(
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.bluetooth_disabled,
                  size: 48,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 12),
                Text(
                  'Aucun appareil connecté',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Connectez un appareil pour effectuer un test',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pushNamed('/manage-devices');
              },
              icon: const Icon(Icons.bluetooth_searching, size: 20),
              label: const Text(
                'Connecter un appareil',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
            ),
          ),
        ],
      );
    }
  }

  Widget _buildInfoRow(IconData icon, String label, String value, Color iconColor) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: iconColor.withAlpha(30),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: iconColor,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: AppTheme.textSecondaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
