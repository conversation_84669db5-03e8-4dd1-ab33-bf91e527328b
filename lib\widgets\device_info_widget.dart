import 'dart:async';
import 'package:flutter/material.dart';
import 'package:befine/models/device_model.dart';
import 'package:befine/services/ble_manager.dart';
import 'package:befine/services/device_service.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:provider/provider.dart';

/// Widget for displaying device information without connection controls
class DeviceInfoWidget extends StatefulWidget {
  /// Constructor
  const DeviceInfoWidget({super.key});

  @override
  State<DeviceInfoWidget> createState() => _DeviceInfoWidgetState();
}

class _DeviceInfoWidgetState extends State<DeviceInfoWidget> {
  int? _batteryLevel;
  Timer? _batteryCheckTimer;

  @override
  void initState() {
    super.initState();
    // Check battery level on init
    _checkBatteryLevel();

    // Set up periodic battery level check
    _batteryCheckTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => _checkBatteryLevel(),
    );
  }

  @override
  void dispose() {
    _batteryCheckTimer?.cancel();
    super.dispose();
  }

  Future<void> _checkBatteryLevel() async {
    final bleManager = Provider.of<BleManager>(context, listen: false);
    if (bleManager.connectionState == BleConnectionState.connected) {
      final batteryLevel = await bleManager.getDeviceBatteryLevel();
      if (mounted) {
        setState(() {
          _batteryLevel = batteryLevel;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<BleManager, DeviceService>(
      builder: (context, bleManager, deviceService, child) {
        return Card(
          elevation: 4,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Appareil en utilisation',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    _buildStatusIndicator(bleManager.connectionState),
                  ],
                ),
                const SizedBox(height: 16),
                _buildDeviceInfo(bleManager, deviceService),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusIndicator(BleConnectionState state) {
    Color color;
    String text;

    switch (state) {
      case BleConnectionState.connected:
        color = Colors.green;
        text = 'Connecté';
        break;
      case BleConnectionState.connecting:
        color = Colors.orange;
        text = 'Connexion en cours';
        break;
      case BleConnectionState.scanning:
        color = Colors.blue;
        text = 'Recherche';
        break;
      case BleConnectionState.error:
        color = Colors.red;
        text = 'Erreur';
        break;
      case BleConnectionState.disconnected:
        color = Colors.grey;
        text = 'Déconnecté';
        break;
    }

    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        const SizedBox(width: 8),
        Text(text, style: TextStyle(color: AppTheme.textPrimaryColor)),
      ],
    );
  }

  Widget _buildDeviceInfo(BleManager bleManager, DeviceService deviceService) {
    if (bleManager.connectionState == BleConnectionState.connected &&
        bleManager.connectedDevice != null) {
      final device = bleManager.connectedDevice!;
      final deviceId = device.remoteId.toString();

      // Try to get device info from DeviceService
      final savedDevice = deviceService.getDeviceById(deviceId);
      final deviceName =
          savedDevice?.customName ??
          (device.platformName.isNotEmpty
              ? device.platformName
              : 'SmartInhealer');

      // Get battery status
      final BatteryStatus batteryStatus = Device.getBatteryStatus(
        _batteryLevel ?? 0,
      );

      // Get battery icon
      IconData batteryIcon;
      Color batteryColor;

      switch (batteryStatus) {
        case BatteryStatus.low:
          batteryIcon = Icons.battery_alert;
          batteryColor = Colors.red;
          break;
        case BatteryStatus.medium:
          batteryIcon = Icons.battery_3_bar;
          batteryColor = Colors.orange;
          break;
        case BatteryStatus.high:
          batteryIcon = Icons.battery_5_bar;
          batteryColor = Colors.green;
          break;
        case BatteryStatus.full:
          batteryIcon = Icons.battery_full;
          batteryColor = Colors.green;
          break;
        case BatteryStatus.unknown:
          batteryIcon = Icons.battery_unknown;
          batteryColor = Colors.grey;
          break;
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.bluetooth_connected, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  deviceName,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppTheme.secondaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'ID: ${deviceId.substring(deviceId.length - 8)}',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(batteryIcon, color: batteryColor, size: 20),
              const SizedBox(width: 8),
              Text(
                _batteryLevel != null
                    ? 'Batterie: $_batteryLevel%'
                    : 'Batterie: --',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.access_time, color: AppTheme.secondaryColor, size: 20),
              const SizedBox(width: 8),
              Text(
                'Connecté depuis: ${_getConnectedTime()}',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          OutlinedButton.icon(
            onPressed: () {
              Navigator.of(context).pushNamed('/manage-devices');
            },
            icon: const Icon(Icons.settings),
            label: const Text('Gérer les appareils'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.primaryColor,
              side: BorderSide(color: AppTheme.primaryColor),
            ),
          ),
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Aucun appareil connecté',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text(
            'Connectez un appareil pour effectuer un test',
            style: TextStyle(fontSize: 14),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pushNamed('/manage-devices');
            },
            icon: const Icon(Icons.bluetooth_searching),
            label: const Text('Connecter un appareil'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      );
    }
  }

  String _getConnectedTime() {
    // In a real implementation, you would track the connection time
    // For now, we'll just return a placeholder
    return 'maintenant';
  }
}
