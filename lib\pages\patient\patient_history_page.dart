import 'package:befine/pages/patient/today_list_view.dart';
import 'package:befine/widgets/history/week_list.dart';
import 'package:flutter/material.dart';
import 'package:befine/theme/app_theme.dart';
import 'package:befine/widgets/history/history_today_view.dart';
import 'package:befine/widgets/history/history_week_view.dart';
import 'package:befine/widgets/history/history_month_view.dart';
import 'package:befine/pages/patient/patient_home_page.dart';

class PatientHistoryPage extends StatefulWidget {
  const PatientHistoryPage({Key? key}) : super(key: key);

  @override
  _PatientHistoryPageState createState() => _PatientHistoryPageState();
}

class _PatientHistoryPageState extends State<PatientHistoryPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // Refresh UI when tab changes
    });
  }

  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (!_isSearching) {
        _searchController.clear();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Title section
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Treatment History',
                    style: TextStyle(
                      color: AppTheme.textPrimaryColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      _isSearching ? Icons.close : Icons.search,
                      color: AppTheme.textPrimaryColor,
                    ),
                    onPressed: _toggleSearch,
                  ),
                ],
              ),
            ),
            if (_isSearching) _buildSearchBar(),
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: const [
                  HistoryTodayView(),
                  HistoryWeekView(),
                  HistoryMonthView(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: AppTheme.surfaceColor,
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search history...',
          hintStyle: TextStyle(color: AppTheme.textTertiaryColor),
          prefixIcon: Icon(Icons.search, color: AppTheme.primaryColor),
          filled: true,
          fillColor: AppTheme.backgroundColor,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.symmetric(vertical: 12),
        ),
        style: TextStyle(color: AppTheme.textPrimaryColor),
        onChanged: (value) {
          // Implement search functionality
          setState(() {});
        },
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: AppTheme.primaryColor,
        unselectedLabelColor: AppTheme.textSecondaryColor,
        indicatorColor: AppTheme.primaryColor,
        indicatorWeight: 3,
        labelStyle: const TextStyle(fontWeight: FontWeight.bold),
        tabs: const [Tab(text: 'Today'), Tab(text: 'Week'), Tab(text: 'Month')],
      ),
    );
  }

  Widget _buildHistoryList(String period) {
    // Get the current date
    final DateTime now = DateTime.now();

    // Generate a list of dates for the current month
    final List<DateTime> monthDates = List.generate(
      30, // Show last 30 days for month view
      (index) => now.subtract(Duration(days: index)),
    );

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: monthDates.length,
      itemBuilder: (context, index) {
        final date = monthDates[index];
        final bool isToday = index == 0;
        final String monthName = _getMonthName(date.month);
        final String formattedDate = "${date.day} $monthName ${date.year}";

        // Random activity count (in a real app, this would come from your database)
        final int activityCount =
            1 + (date.day % 5); // Between 1 and 5 activities

        return Card(
          margin: const EdgeInsets.symmetric(vertical: 8),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color:
                  isToday
                      ? AppTheme.primaryColor.withAlpha(77)
                      : Colors.transparent,
              width: 1.5,
            ),
          ),
          child: InkWell(
            onTap: () {
              // Navigate to detailed view for this date
              // You would implement this navigation
            },
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Date container with colored background
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color:
                          isToday
                              ? AppTheme.primaryColor
                              : AppTheme.backgroundColor,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color:
                            isToday
                                ? AppTheme.primaryColor
                                : AppTheme.textTertiaryColor.withAlpha(77),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          date.day.toString(),
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color:
                                isToday
                                    ? Colors.white
                                    : AppTheme.textPrimaryColor,
                          ),
                        ),
                        Text(
                          _getDayName(
                            date.weekday,
                          ).substring(0, 3), // Abbreviated day name
                          style: TextStyle(
                            fontSize: 14,
                            color:
                                isToday
                                    ? Colors.white
                                    : AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isToday ? "Today, $formattedDate" : formattedDate,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          "$activityCount activities recorded",
                          style: TextStyle(color: AppTheme.textSecondaryColor),
                        ),
                        const SizedBox(height: 8),
                        // Progress indicator
                        LinearProgressIndicator(
                          value: activityCount / 5, // 5 is max activities
                          backgroundColor: AppTheme.backgroundColor,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppTheme.primaryColor,
                          ),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ],
                    ),
                  ),
                  // Arrow icon
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: AppTheme.textTertiaryColor,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Helper method to get day name
  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return "Monday";
      case 2:
        return "Tuesday";
      case 3:
        return "Wednesday";
      case 4:
        return "Thursday";
      case 5:
        return "Friday";
      case 6:
        return "Saturday";
      case 7:
        return "Sunday";
      default:
        return "";
    }
  }

  // Helper method to get month name
  String _getMonthName(int month) {
    switch (month) {
      case 1:
        return "Jan";
      case 2:
        return "Feb";
      case 3:
        return "Mar";
      case 4:
        return "Apr";
      case 5:
        return "May";
      case 6:
        return "Jun";
      case 7:
        return "Jul";
      case 8:
        return "Aug";
      case 9:
        return "Sep";
      case 10:
        return "Oct";
      case 11:
        return "Nov";
      case 12:
        return "Dec";
      default:
        return "";
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
